// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.DialogueSystem.Wrappers
{

    /// <summary>
    /// This wrapper class keeps references intact if you switch between the 
    /// compiled assembly and source code versions of the original class.
    /// </summary>
    [HelpURL("https://pixelcrushers.com/dialogue_system/manual2x/html/quests.html#questIndicators")]
    [AddComponentMenu("Pixel Crushers/Dialogue System/Quest System/Quest Indicators/Quest State Indicator")]
    public class QuestStateIndicator : PixelCrushers.DialogueSystem.QuestStateIndicator
    {
    }

}
