// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.DialogueSystem.Wrappers
{

    /// <summary>
    /// This wrapper class keeps references intact if you switch between the 
    /// compiled assembly and source code versions of the original class.
    /// </summary>
	[AddComponentMenu("Pixel Crushers/Dialogue System/UI/Unity UI/Effects/Unity UI Color Text")]
    public class UnityUIColorText : PixelCrushers.DialogueSystem.UnityUIColorText
    {
    }

}
