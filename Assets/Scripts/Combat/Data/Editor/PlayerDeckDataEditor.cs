#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using EOP.Combat.Data;

namespace EOP.Combat.Data.Editor
{
    [CustomEditor(typeof(BasicDecoder))]
    public class BasicDecoderEditor : UnityEditor.Editor
    {
        private BasicDecoder decoder;
        private Vector2 scrollPosition;
        private bool showDeckData = true;
        private string newKey = "";
        private string newValue = "";

        private void OnEnable()
        {
            decoder = (BasicDecoder)target;
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            // Draw default Inspector
            DrawDefaultInspector();

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Player Deck Data Management", EditorStyles.boldLabel);

            // Deck data foldout panel
            showDeckData = EditorGUILayout.Foldout(showDeckData, "Deck Data Details", true);
            if (showDeckData)
            {
                DrawDeckDataSection();
            }

            EditorGUILayout.Space();
            DrawUtilityButtons();

            serializedObject.ApplyModifiedProperties();

            if (GUI.changed)
            {
                EditorUtility.SetDirty(decoder);
            }
        }

        private void DrawDeckDataSection()
        {
            EditorGUILayout.BeginVertical("box");

            // Display statistics
            List<PlayerDeckEntry> entries = decoder.GetDeckEntries();
            EditorGUILayout.LabelField($"Entry Count: {entries.Count}", EditorStyles.helpBox);
            EditorGUILayout.LabelField($"Dictionary Count: {decoder.playerDeck.Count}", EditorStyles.helpBox);

            EditorGUILayout.Space();

            // Add new entry
            EditorGUILayout.LabelField("Add New Entry", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            newKey = EditorGUILayout.TextField(new GUIContent("Key:", "Piece name or identifier"), newKey);
            newValue = EditorGUILayout.TextField(new GUIContent("Value:", "Skill ID or corresponding value"), newValue);
            if (GUILayout.Button("Add", GUILayout.Width(50)))
            {
                if (!string.IsNullOrEmpty(newKey) && !string.IsNullOrEmpty(newValue))
                {
                    decoder.AddDeckEntry(newKey, newValue);
                    newKey = "";
                    newValue = "";
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Key and Value cannot be empty", "OK");
                }
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            // Display existing entries
            EditorGUILayout.LabelField("Existing Entries", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("Key: Piece identifier (e.g., 'RedL', 'BlueT') → Value: Skill ID (e.g., 'BR01', 'BR02')", MessageType.Info);
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(200));

            for (int i = 0; i < entries.Count; i++)
            {
                EditorGUILayout.BeginHorizontal("box");

                EditorGUILayout.LabelField($"{i + 1}.", GUILayout.Width(25));
                EditorGUILayout.LabelField(new GUIContent(entries[i].TetrominoID, "Piece identifier"), GUILayout.Width(100));
                EditorGUILayout.LabelField("→", GUILayout.Width(20));
                EditorGUILayout.LabelField(new GUIContent(entries[i].SkillID, "Skill ID"), GUILayout.Width(100));

                if (GUILayout.Button("Delete", GUILayout.Width(60)))
                {
                    if (EditorUtility.DisplayDialog("Confirm Delete", $"Are you sure you want to delete entry {entries[i].TetrominoID}?", "Yes", "Cancel"))
                    {
                        decoder.RemoveDeckEntry(entries[i].TetrominoID);
                    }
                }

                EditorGUILayout.EndHorizontal();
            }

            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();
        }

        private void DrawUtilityButtons()
        {
            EditorGUILayout.LabelField("Utility Tools", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button(new GUIContent("Validate Data", "Check deck data for errors and duplicates")))
            {
                bool isValid = decoder.ValidateDeckData();
                string message = isValid ? "Deck data validation passed" : "Deck data validation failed, check Console for details";
                EditorUtility.DisplayDialog("Validation Result", message, "OK");
            }

            if (GUILayout.Button(new GUIContent("Reset to Default", "Reset to default deck configuration")))
            {
                if (EditorUtility.DisplayDialog("Confirm Reset", "Are you sure you want to reset to default deck? This will clear all custom data.", "Yes", "Cancel"))
                {
                    decoder.ResetToDefaultDeck();
                }
            }

            if (GUILayout.Button(new GUIContent("Clear Data", "Remove all deck entries")))
            {
                if (EditorUtility.DisplayDialog("Confirm Clear", "Are you sure you want to clear all deck data?", "Yes", "Cancel"))
                {
                    decoder.ClearDeckData();
                }
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button(new GUIContent("Sync List → Dict", "Convert list data to dictionary")))
            {
                decoder.ConvertListToDictionary();
                Debug.Log("List data synchronized to dictionary");
            }

            if (GUILayout.Button(new GUIContent("Sync Dict → List", "Convert dictionary data to list")))
            {
                decoder.ConvertDictionaryToList();
                Debug.Log("Dictionary data synchronized to list");
            }

            if (GUILayout.Button(new GUIContent("Print Info", "Log deck information to console")))
            {
                decoder.PrintDeckInfo();
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();
            EditorGUILayout.HelpBox("Sync List → Dict: Updates the runtime dictionary from the serialized list\n" +
                                   "Sync Dict → List: Updates the serialized list from the runtime dictionary\n" +
                                   "Use these when you modify data programmatically", MessageType.Info);
        }
    }

    /// <summary>
    /// Custom editor for Player Deck Data Manager
    /// </summary>
    [CustomPropertyDrawer(typeof(PlayerDeckDataManager))]
    public class PlayerDeckDataManagerPropertyDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            // Draw foldout header
            property.isExpanded = EditorGUI.Foldout(new Rect(position.x, position.y, position.width, EditorGUIUtility.singleLineHeight),
                                                   property.isExpanded, new GUIContent(label.text, "Player deck data manager containing piece-to-skill mappings"), true);

            if (property.isExpanded)
            {
                EditorGUI.indentLevel++;

                // Get deckEntries property
                SerializedProperty deckEntriesProp = property.FindPropertyRelative("deckEntries");

                if (deckEntriesProp != null)
                {
                    Rect listRect = new Rect(position.x, position.y + EditorGUIUtility.singleLineHeight + 2,
                                           position.width, position.height - EditorGUIUtility.singleLineHeight - 2);
                    EditorGUI.PropertyField(listRect, deckEntriesProp, new GUIContent("Deck Entries", "List of piece-to-skill mappings"), true);
                }

                EditorGUI.indentLevel--;
            }

            EditorGUI.EndProperty();
        }

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            if (!property.isExpanded)
                return EditorGUIUtility.singleLineHeight;

            SerializedProperty deckEntriesProp = property.FindPropertyRelative("deckEntries");
            if (deckEntriesProp != null)
            {
                return EditorGUIUtility.singleLineHeight + EditorGUI.GetPropertyHeight(deckEntriesProp, true) + 2;
            }

            return EditorGUIUtility.singleLineHeight;
        }
    }
}
#endif
