using System.Collections;
using System.Collections.Generic;
using UnityEngine;

// uses combatbuff

namespace EOP.combat
{
    public class WeaknessBuff : CombatBuff
    {
        public float _strengthAmount;

        public WeaknessBuff() : base("Weakness")
        {
            
        }

        public override float ProcessBuff(float attackDamage)
        {
            return attackDamage;
        }
    }
}
