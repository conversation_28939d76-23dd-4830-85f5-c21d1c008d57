using System.Collections;
using System.Collections.Generic;
using UnityEngine;

// uses combatbuff

namespace EOP.combat
{
    public class SpeedUpBuff : CombatBuff
    {
        public float _strengthAmount;

        public SpeedUpBuff() : base("SpeedUp")
        {
            
        }

        public override float ProcessBuff(float attackDamage)
        {
            return attackDamage;
        }
    }
}
