using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using EOP.combat;

namespace EOP.Skills.Effects
{
    public class AttackSkillEffect : SkillEffect
    {
        [Header("攻击效果设置")]
        public bool useInstinctBonus = false;
        public int instinctBonusDamage = 1;

        [Header("目标选择")]
        private TargetSelector targetSelector;

        protected override void Start()
        {
            base.Start();
            targetSelector = FindObjectOfType<TargetSelector>();
            if (targetSelector == null)
            {
                GameObject targetSelectorObj = new GameObject("TargetSelector");
                targetSelector = targetSelectorObj.AddComponent<TargetSelector>();
            }
        }

        public override void Execute(SkillData skillData, int clearCount, float dropTime)
        {
            if (!CanExecute(skillData)) return;

            int damage = CalculateAttackDamage(skillData, clearCount, dropTime);

            // 根据目标类型执行效果
            if (targetSelector != null)
            {
                targetSelector.ExecuteEffectByTargetType(skillData, damage);
                Debug.Log($"执行攻击技能: {skillData.skillName}, 伤害: {damage}, 目标: {skillData.GetTargetTypeText()}");
            }
            else
            {
                // 回退到默认攻击方式
                combatManager.PlayerAttackTargetEnemy(damage);
                Debug.Log($"执行攻击技能: {skillData.skillName}, 伤害: {damage} (默认目标)");
            }
        }

        private int CalculateAttackDamage(SkillData skillData, int clearCount, float dropTime)
        {
            int baseDamage = CalculateEffectValue(skillData, clearCount, dropTime);
            
            // 检查直觉状态加成
            if (useInstinctBonus && IsInInstinctState())
            {
                baseDamage += instinctBonusDamage;
            }
            
            return baseDamage;
        }

        private bool IsInInstinctState()
        {
            // 这里需要检查玩家是否处于直觉状态
            // 可能需要在CombatPlayer中添加相应的状态检查
            // 暂时返回false，后续可以扩展
            return false;
        }

        public override bool CanExecute(SkillData skillData)
        {
            if (!base.CanExecute(skillData)) return false;
            
            // 检查速度条件
            return CheckSpeedCondition(skillData);
        }
    }
}
