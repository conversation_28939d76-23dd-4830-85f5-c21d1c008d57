using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using EOP.combat;
using EOP.Tetris;

namespace EOP.Skills
{
    public class SkillManager : MonoBehaviour
    {
        [Header("技能系统设置")]
        [SerializeField] private SkillDatabase skillDatabase;
        [SerializeField] private SkillStorage skillStorage;
        
        [Header("战斗系统引用")]
        private CombatManager combatManager;
        private CombatPlayer combatPlayer;
        private TetrisGame tetrisGame;
        private TargetSelector targetSelector;

        private void Start()
        {
            // 获取系统引用
            combatManager = FindObjectOfType<CombatManager>();
            combatPlayer = FindObjectOfType<CombatPlayer>();
            tetrisGame = FindObjectOfType<TetrisGame>();
            targetSelector = FindObjectOfType<TargetSelector>();

            if (skillStorage == null)
                skillStorage = GetComponent<SkillStorage>();

            if (skillStorage == null)
                skillStorage = gameObject.AddComponent<SkillStorage>();

            if (targetSelector == null)
            {
                GameObject targetSelectorObj = new GameObject("TargetSelector");
                targetSelector = targetSelectorObj.AddComponent<TargetSelector>();
            }
        }

        /// <summary>
        /// 处理方块落地技能
        /// </summary>
        public void ProcessLandSkill(string skillId, float dropTime)
        {
            if (skillDatabase == null)
            {
                Debug.LogWarning("SkillDatabase 未设置");
                return;
            }

            SkillData skillData = skillDatabase.GetSkill(skillId);
            if (skillData == null)
            {
                Debug.LogWarning($"未找到技能: {skillId}");
                return;
            }

            if (skillData.HasTriggerType(SkillTriggerType.OnLand))
            {
                ExecuteSkillEffect(skillData, 1, dropTime);
            }
        }

        /// <summary>
        /// 处理消除行技能
        /// </summary>
        public void ProcessClearSkill(string skillId, int clearCount)
        {
            if (skillDatabase == null)
            {
                Debug.LogWarning("SkillDatabase 未设置");
                return;
            }

            SkillData skillData = skillDatabase.GetSkill(skillId);
            if (skillData == null)
            {
                Debug.LogWarning($"未找到技能: {skillId}");
                return;
            }

            if (skillData.HasTriggerType(SkillTriggerType.OnClear))
            {
                // 检查是否应该存储技能
                if (ShouldStoreSkill(skillData, clearCount))
                {
                    skillStorage.StoreSkill(skillId, clearCount);
                }
                else
                {
                    ExecuteSkillEffect(skillData, clearCount, 0f);
                }
            }
        }

        /// <summary>
        /// 执行技能效果
        /// </summary>
        private void ExecuteSkillEffect(SkillData skillData, int clearCount, float dropTime)
        {
            if (!CheckSkillConditions(skillData, dropTime))
            {
                Debug.Log($"技能 {skillData.skillName} 条件不满足");
                return;
            }

            int effectValue = CalculateEffectValue(skillData, clearCount, dropTime);
            
            switch (skillData.effectType)
            {
                case SkillEffectType.Attack:
                    ExecuteAttackEffect(skillData, effectValue);
                    break;
                    
                case SkillEffectType.Heal:
                    combatManager?.ChangePlayerHP(effectValue);
                    break;
                    
                case SkillEffectType.Shield:
                    combatManager?.ChangePlayerShield(effectValue);
                    break;
                    
                case SkillEffectType.Speed:
                    combatManager?.ChangePlayerSpeed(effectValue);
                    break;
                    
                case SkillEffectType.Buff:
                    EOP.Skills.Effects.BuffSkillEffect.ExecuteBuff(skillData, clearCount, dropTime, combatManager);
                    break;
                    
                case SkillEffectType.Special:
                    ExecuteSpecialEffect(skillData, clearCount, dropTime);
                    break;
            }
            
            Debug.Log($"执行技能: {skillData.skillName}, 效果值: {effectValue}");
        }

        /// <summary>
        /// 检查技能触发条件
        /// </summary>
        private bool CheckSkillConditions(SkillData skillData, float dropTime)
        {
            // 检查速度阈值
            if (skillData.speedThreshold > 0 && combatPlayer != null)
            {
                if (combatPlayer._speed < skillData.speedThreshold)
                    return false;
            }

            // 检查直觉状态（如果需要的话）
            if (skillData.requiresInstinct)
            {
                // 这里需要检查玩家是否处于直觉状态
                // 可能需要在CombatPlayer中添加相应的状态检查
            }

            return true;
        }

        /// <summary>
        /// 计算技能效果数值
        /// </summary>
        private int CalculateEffectValue(SkillData skillData, int clearCount, float dropTime)
        {
            int value = skillData.baseValue;
            
            // 根据消除数量缩放
            if (skillData.scalesWithClear)
            {
                value *= clearCount;
            }
            
            // 根据速度影响
            if (combatPlayer != null && skillData.speedMultiplier != 1f)
            {
                float speedBonus = combatPlayer._speed * skillData.speedMultiplier;
                value = Mathf.RoundToInt(value + speedBonus);
            }
            
            return Mathf.Max(1, value); // 确保至少为1
        }

        /// <summary>
        /// 执行攻击效果
        /// </summary>
        private void ExecuteAttackEffect(SkillData skillData, int damage)
        {
            if (combatManager != null && targetSelector != null)
            {
                targetSelector.ExecuteAttackByTargetType(skillData.targetType, damage);
                Debug.Log($"攻击效果: {damage} 伤害, 目标: {skillData.GetTargetTypeText()}");
            }
            else if (combatManager != null)
            {
                // 回退到默认攻击方式
                combatManager.PlayerAttackTargetEnemy(damage);
                Debug.Log($"攻击效果: {damage} 伤害 (默认目标)");
            }
        }

        /// <summary>
        /// 执行特殊效果
        /// </summary>
        private void ExecuteSpecialEffect(SkillData skillData, int clearCount, float dropTime)
        {
            switch (skillData.skillId)
            {
                case "BR13":
                    // 设置直觉状态
                    if (combatPlayer != null && combatPlayer._speed >= 10)
                    {
                        // 增加行动次数
                        // 这里需要实现直觉状态的逻辑
                    }
                    break;
                    
                case "BR12":
                    // 特殊计数器逻辑
                    break;
            }
        }

        /// <summary>
        /// 判断是否应该存储技能
        /// </summary>
        private bool ShouldStoreSkill(SkillData skillData, int clearCount)
        {
            // 根据游戏规则，当方块被消除时，技能会被存储到保留格
            // 这里可以添加更复杂的逻辑来决定是否存储
            return skillStorage.CanStoreMoreSkills();
        }

        /// <summary>
        /// 获取技能描述
        /// </summary>
        public string GetSkillDescription(string skillId)
        {
            if (skillDatabase == null) return "";
            
            SkillData skillData = skillDatabase.GetSkill(skillId);
            return skillData?.description ?? "";
        }

        /// <summary>
        /// 使用存储的技能
        /// </summary>
        public void UseStoredSkill(int index)
        {
            if (skillStorage != null)
            {
                skillStorage.UseStoredSkill(index);
            }
        }

        /// <summary>
        /// 处理多触发类型技能
        /// </summary>
        public void ProcessMultiTriggerSkill(string skillId, SkillTriggerType triggerType, int clearCount = 1, float dropTime = 0f)
        {
            if (skillDatabase == null)
            {
                Debug.LogWarning("SkillDatabase 未设置");
                return;
            }

            SkillData skillData = skillDatabase.GetSkill(skillId);
            if (skillData == null)
            {
                Debug.LogWarning($"未找到技能: {skillId}");
                return;
            }

            if (skillData.HasTriggerType(triggerType))
            {
                // 计算触发次数（如果技能有多个触发类型，每个满足的触发类型都会增加触发次数）
                int triggerCount = GetTriggerCount(skillData, triggerType);

                for (int i = 0; i < triggerCount; i++)
                {
                    if (triggerType == SkillTriggerType.OnClear && ShouldStoreSkill(skillData, clearCount))
                    {
                        skillStorage.StoreSkill(skillId, clearCount);
                    }
                    else
                    {
                        ExecuteSkillEffect(skillData, clearCount, dropTime);
                    }
                }

                Debug.Log($"技能 {skillData.skillName} 因 {triggerType} 触发了 {triggerCount} 次");
            }
        }

        /// <summary>
        /// 获取触发次数
        /// </summary>
        private int GetTriggerCount(SkillData skillData, SkillTriggerType triggerType)
        {
            // 基础触发次数为1
            int count = 1;

            // 如果技能有多个相同的触发类型，可以增加触发次数
            // 这里可以根据具体需求调整逻辑

            return count;
        }
    }
}
