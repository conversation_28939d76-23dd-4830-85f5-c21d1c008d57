using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace EOP.Skills
{
    [CreateAssetMenu(fileName = "New Skill Database", menuName = "Skills/Skill Database")]
    public class SkillDatabase : ScriptableObject
    {
        [Header("技能数据库")]
        public List<SkillData> skills = new List<SkillData>();
        
        private Dictionary<string, SkillData> skillLookup;
        public void BuildLookupTable()
        {
            skillLookup = new Dictionary<string, SkillData>();
            foreach (var skill in skills)
            {
                if (!string.IsNullOrEmpty(skill.skillId))
                {
                    skillLookup[skill.skillId] = skill;
                }
            }
        }

        public SkillData GetSkill(string skillId)
        {
            if (skillLookup == null)
                BuildLookupTable();

            
            return skillLookup.ContainsKey(skillId) ? skillLookup[skillId] : null;
        }

        public bool HasSkill(string skillId)
        {
            if (skillLookup == null)
                BuildLookupTable();
                
            return skillLookup.ContainsKey(skillId);
        }

        /// <summary>
        /// 添加新技能到数据库
        /// </summary>
        public void AddSkill(SkillData skill)
        {
            if (skill == null || string.IsNullOrEmpty(skill.skillId))
            {
                Debug.LogWarning("无法添加无效的技能");
                return;
            }

            if (HasSkill(skill.skillId))
            {
                Debug.LogWarning($"技能 {skill.skillId} 已存在");
                return;
            }

            skills.Add(skill);
            BuildLookupTable();
        }

        /// <summary>
        /// 从数据库移除技能
        /// </summary>
        public bool RemoveSkill(string skillId)
        {
            for (int i = 0; i < skills.Count; i++)
            {
                if (skills[i].skillId == skillId)
                {
                    skills.RemoveAt(i);
                    BuildLookupTable();
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 获取所有技能ID
        /// </summary>
        public List<string> GetAllSkillIds()
        {
            List<string> ids = new List<string>();
            foreach (var skill in skills)
            {
                if (!string.IsNullOrEmpty(skill.skillId))
                {
                    ids.Add(skill.skillId);
                }
            }
            return ids;
        }

        /// <summary>
        /// 根据触发类型获取技能
        /// </summary>
        public List<SkillData> GetSkillsByTriggerType(SkillTriggerType triggerType)
        {
            List<SkillData> result = new List<SkillData>();
            foreach (var skill in skills)
            {
                if (skill.triggerType == triggerType)
                {
                    result.Add(skill);
                }
            }
            return result;
        }

        /// <summary>
        /// 根据效果类型获取技能
        /// </summary>
        public List<SkillData> GetSkillsByEffectType(SkillEffectType effectType)
        {
            List<SkillData> result = new List<SkillData>();
            foreach (var skill in skills)
            {
                if (skill.effectType == effectType)
                {
                    result.Add(skill);
                }
            }
            return result;
        }

        /// <summary>
        /// 验证数据库完整性
        /// </summary>
        public bool ValidateDatabase()
        {
            bool isValid = true;
            HashSet<string> uniqueIds = new HashSet<string>();

            for (int i = 0; i < skills.Count; i++)
            {
                SkillData skill = skills[i];
                
                // 检查技能ID是否为空
                if (string.IsNullOrEmpty(skill.skillId))
                {
                    Debug.LogError($"技能 {i} 的ID为空");
                    isValid = false;
                    continue;
                }

                // 检查技能ID是否重复
                if (uniqueIds.Contains(skill.skillId))
                {
                    Debug.LogError($"技能ID {skill.skillId} 重复");
                    isValid = false;
                }
                else
                {
                    uniqueIds.Add(skill.skillId);
                }

                // 检查技能名称是否为空
                if (string.IsNullOrEmpty(skill.skillName))
                {
                    Debug.LogWarning($"技能 {skill.skillId} 的名称为空");
                }

                // 检查基础数值是否合理
                if (skill.baseValue < 0)
                {
                    Debug.LogWarning($"技能 {skill.skillId} 的基础数值为负数");
                }
            }

            if (isValid)
            {
                Debug.Log("技能数据库验证通过");
            }

            return isValid;
        }

        /// <summary>
        /// 清空数据库
        /// </summary>
        public void ClearDatabase()
        {
            skills.Clear();
            skillLookup?.Clear();
        }

        /// <summary>
        /// 获取数据库统计信息
        /// </summary>
        public string GetDatabaseStats()
        {
            int landSkills = GetSkillsByTriggerType(SkillTriggerType.OnLand).Count;
            int clearSkills = GetSkillsByTriggerType(SkillTriggerType.OnClear).Count;
            int persistentSkills = GetSkillsByTriggerType(SkillTriggerType.Persistent).Count;

            int attackSkills = GetSkillsByEffectType(SkillEffectType.Attack).Count;
            int buffSkills = GetSkillsByEffectType(SkillEffectType.Buff).Count;
            int healSkills = GetSkillsByEffectType(SkillEffectType.Heal).Count;
            int shieldSkills = GetSkillsByEffectType(SkillEffectType.Shield).Count;
            int specialSkills = GetSkillsByEffectType(SkillEffectType.Special).Count;

            return $"技能总数: {skills.Count}\n" +
                   $"触发类型 - 落地: {landSkills}, 消除: {clearSkills}, 持续: {persistentSkills}\n" +
                   $"效果类型 - 攻击: {attackSkills}, 增益: {buffSkills}, 治疗: {healSkills}, 护盾: {shieldSkills}, 特殊: {specialSkills}";
        }

#if UNITY_EDITOR
        /// <summary>
        /// 编辑器专用：重新构建查找表
        /// </summary>
        [ContextMenu("重新构建查找表")]
        public void RebuildLookupTable()
        {
            BuildLookupTable();
            Debug.Log("查找表已重新构建");
        }

        /// <summary>
        /// 编辑器专用：验证并修复数据库
        /// </summary>
        [ContextMenu("验证并修复数据库")]
        public void ValidateAndFixDatabase()
        {
            ValidateDatabase();
            BuildLookupTable();
        }
#endif
    }
}
