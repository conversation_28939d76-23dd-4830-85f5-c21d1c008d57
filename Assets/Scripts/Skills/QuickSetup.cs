using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using EOP.Skills;
using EOP.combat;
using EOP.Tetris;
using EOP.Skills.UI;

#if UNITY_EDITOR
using UnityEditor;
using System.IO;
#endif

namespace EOP.Skills
{
    public class QuickSetup : MonoBehaviour
    {
        [Header("快速设置选项")]
        [SerializeField] private bool setupOnStart = true;
        [SerializeField] private bool createDatabase = true;
        [SerializeField] private bool setupManagers = true;
        [SerializeField] private bool setupUI = true;

        [Header("设置状态")]
        [SerializeField] private bool isSetupComplete = false;

        private void Start()
        {
            if (setupOnStart && !isSetupComplete)
            {
                StartCoroutine(PerformQuickSetup());
            }
        }

        public IEnumerator PerformQuickSetup()
        {
            Debug.Log("开始快速设置技能系统...");

            // 1. 创建技能数据库
            if (createDatabase)
            {
                yield return StartCoroutine(SetupDatabase());
            }

            // 2. 设置管理器
            if (setupManagers)
            {
                yield return StartCoroutine(SetupManagers());
            }

            // 3. 设置UI
            if (setupUI)
            {
                yield return StartCoroutine(SetupUI());
            }

            // 4. 验证设置
            yield return StartCoroutine(ValidateSetup());

            isSetupComplete = true;
            Debug.Log("技能系统快速设置完成！");
        }

        private IEnumerator SetupDatabase()
        {
            Debug.Log("设置技能数据库...");

            // 查找现有的数据库
            SkillDatabase database = FindObjectOfType<SkillDatabase>();
            if (database == null)
            {
                // 尝试从Resources加载
                database = Resources.Load<SkillDatabase>("SkillDatabase");
            }

            if (database == null)
            {
#if UNITY_EDITOR
                // 在编辑器中创建数据库
                CreateDatabaseInEditor();
#else
                Debug.LogWarning("未找到技能数据库，请在编辑器中创建");
#endif
            }

            yield return null;
        }

#if UNITY_EDITOR
        private void CreateDatabaseInEditor()
        {
            string resourcesPath = "Assets/Scripts/Skills/Resources";
            if (!Directory.Exists(resourcesPath))
            {
                Directory.CreateDirectory(resourcesPath);
            }

            SkillDatabase database = ScriptableObject.CreateInstance<SkillDatabase>();
            InitializeDefaultSkills(database);

            string assetPath = Path.Combine(resourcesPath, "SkillDatabase.asset");
            AssetDatabase.CreateAsset(database, assetPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            Debug.Log($"技能数据库已创建: {assetPath}");
        }

        private void InitializeDefaultSkills(SkillDatabase database)
        {
            // 添加所有默认技能
            database.AddSkill(new SkillData("BR01", "力量觉醒", "获得力量(1)", SkillTriggerType.OnLand, SkillEffectType.Buff, 1));
            database.AddSkill(new SkillData("BR02", "精准打击", "对目标造成x点伤害", SkillTriggerType.OnClear, SkillEffectType.Attack, 1) { scalesWithClear = true });
            database.AddSkill(new SkillData("BR03", "快速攻击", "对目标造成1点伤害", SkillTriggerType.OnClear, SkillEffectType.Attack, 1));
            database.AddSkill(new SkillData("BR05", "防御姿态", "获得护盾(x)", SkillTriggerType.OnLand, SkillEffectType.Shield, 1) { scalesWithClear = true });
            database.AddSkill(new SkillData("BR07", "速度力量", "获得力量(1)；下落速度(3)：获得力量(1)", SkillTriggerType.OnLand, SkillEffectType.Buff, 1) { speedThreshold = 3f });
            database.AddSkill(new SkillData("BR12", "蓄力", "增加计数器", SkillTriggerType.OnLand, SkillEffectType.Special, 4));
            database.AddSkill(new SkillData("BR13", "高速直觉", "下落速度(10)：获得行动次数(1)；直觉", SkillTriggerType.OnLand, SkillEffectType.Special, 1) { speedThreshold = 10f });
            database.AddSkill(new SkillData("BR14", "冲击打击", "对目标造成2点伤害；下落速度(8)：获得力量(1)", SkillTriggerType.OnLand, SkillEffectType.Attack, 2) { speedThreshold = 8f });
            database.AddSkill(new SkillData("BR17", "预留技能", "预留技能位", SkillTriggerType.OnLand, SkillEffectType.Special, 0));
        }
#endif

        private IEnumerator SetupManagers()
        {
            Debug.Log("设置管理器...");

            // 查找或创建SkillManager
            SkillManager skillManager = FindObjectOfType<SkillManager>();
            if (skillManager == null)
            {
                GameObject managerObj = new GameObject("SkillManager");
                skillManager = managerObj.AddComponent<SkillManager>();
                Debug.Log("创建了SkillManager");
            }

            // 查找或创建SkillStorage
            SkillStorage skillStorage = FindObjectOfType<SkillStorage>();
            if (skillStorage == null)
            {
                skillStorage = skillManager.gameObject.AddComponent<SkillStorage>();
                Debug.Log("创建了SkillStorage");
            }

            // 查找或创建SkillSystemIntegrator
            SkillSystemIntegrator integrator = FindObjectOfType<SkillSystemIntegrator>();
            if (integrator == null)
            {
                integrator = skillManager.gameObject.AddComponent<SkillSystemIntegrator>();
                Debug.Log("创建了SkillSystemIntegrator");
            }

            // 查找或创建TargetSelector
            TargetSelector targetSelector = FindObjectOfType<TargetSelector>();
            if (targetSelector == null)
            {
                GameObject targetSelectorObj = new GameObject("TargetSelector");
                targetSelector = targetSelectorObj.AddComponent<TargetSelector>();
                Debug.Log("创建了TargetSelector");
            }

            yield return null;
        }

        private IEnumerator SetupUI()
        {
            Debug.Log("设置UI...");

            // 查找Canvas
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                Debug.LogWarning("未找到Canvas，跳过UI设置");
                yield break;
            }

            // 查找或创建SkillStorageUI
            SkillStorageUI skillUI = FindObjectOfType<SkillStorageUI>();
            if (skillUI == null)
            {
                GameObject uiObj = new GameObject("SkillStorageUI");
                uiObj.transform.SetParent(canvas.transform, false);
                skillUI = uiObj.AddComponent<SkillStorageUI>();
                
                // 设置UI位置
                RectTransform rectTransform = uiObj.GetComponent<RectTransform>();
                if (rectTransform == null)
                {
                    rectTransform = uiObj.AddComponent<RectTransform>();
                }
                
                rectTransform.anchorMin = new Vector2(0, 0);
                rectTransform.anchorMax = new Vector2(0, 0);
                rectTransform.anchoredPosition = new Vector2(100, 100);
                rectTransform.sizeDelta = new Vector2(300, 100);
                
                Debug.Log("创建了SkillStorageUI");
            }

            yield return null;
        }

        private IEnumerator ValidateSetup()
        {
            Debug.Log("验证设置...");

            bool isValid = true;
            
            // 检查核心组件
            if (FindObjectOfType<SkillManager>() == null)
            {
                Debug.LogError("SkillManager 未找到");
                isValid = false;
            }

            if (FindObjectOfType<SkillStorage>() == null)
            {
                Debug.LogError("SkillStorage 未找到");
                isValid = false;
            }

            // 检查数据库
            SkillDatabase database = Resources.Load<SkillDatabase>("SkillDatabase");
            if (database == null)
            {
                Debug.LogError("SkillDatabase 未找到");
                isValid = false;
            }
            else if (database.skills.Count == 0)
            {
                Debug.LogWarning("SkillDatabase 为空");
            }

            // 检查现有系统
            if (FindObjectOfType<CombatManager>() == null)
            {
                Debug.LogWarning("CombatManager 未找到，某些功能可能无法正常工作");
            }

            if (FindObjectOfType<TetrisGrid>() == null)
            {
                Debug.LogWarning("TetrisGrid 未找到，某些功能可能无法正常工作");
            }

            // 检查目标选择器
            if (FindObjectOfType<TargetSelector>() == null)
            {
                Debug.LogWarning("TargetSelector 未找到，目标选择功能可能无法正常工作");
            }

            if (isValid)
            {
                Debug.Log("✓ 技能系统验证通过");
            }
            else
            {
                Debug.LogError("✗ 技能系统验证失败");
            }

            yield return null;
        }

        [ContextMenu("执行快速设置")]
        public void ExecuteQuickSetup()
        {
            StartCoroutine(PerformQuickSetup());
        }

        [ContextMenu("重置设置状态")]
        public void ResetSetupStatus()
        {
            isSetupComplete = false;
            Debug.Log("设置状态已重置");
        }

        [ContextMenu("验证当前设置")]
        public void ValidateCurrentSetup()
        {
            StartCoroutine(ValidateSetup());
        }
    }
}
