#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.IO;

namespace EOP.Skills
{
    public class SkillDatabaseCreator : EditorWindow
    {
        private string databaseName = "SkillDatabase";
        private string savePath = "Assets/Scripts/Skills/Resources";
        private bool includeTargetExamples = true;

        [MenuItem("Tools/Skills/Create Skill Database")]
        public static void ShowWindow()
        {
            GetWindow<SkillDatabaseCreator>("Skill Database Creator");
        }

        private void OnGUI()
        {
            GUILayout.Label("Skill Database Creator", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            databaseName = EditorGUILayout.TextField(new GUIContent("Database Name:", "Name for the skill database file"), databaseName);
            savePath = EditorGUILayout.TextField(new GUIContent("Save Path:", "Directory where the database will be saved"), savePath);

            EditorGUILayout.Space();
            includeTargetExamples = EditorGUILayout.Toggle(new GUIContent("Include Target Examples:", "Add example skills with different target types"), includeTargetExamples);

            EditorGUILayout.Space();

            if (GUILayout.Button("Create Skill Database"))
            {
                CreateSkillDatabase();
            }

            EditorGUILayout.Space();
            EditorGUILayout.HelpBox("This will create a new SkillDatabase ScriptableObject file", MessageType.Info);

            EditorGUILayout.Space();
            if (GUILayout.Button("Create and Initialize Default Skills"))
            {
                CreateAndInitializeDatabase();
            }

            EditorGUILayout.HelpBox("This will create the database and add all default BR series skills" +
                                   (includeTargetExamples ? " plus target selection examples" : ""), MessageType.Info);
        }

        private void CreateSkillDatabase()
        {
            // 确保目录存在
            if (!Directory.Exists(savePath))
            {
                Directory.CreateDirectory(savePath);
            }

            // 创建SkillDatabase实例
            SkillDatabase database = CreateInstance<SkillDatabase>();

            // 保存为资源文件
            string fullPath = Path.Combine(savePath, databaseName + ".asset");
            AssetDatabase.CreateAsset(database, fullPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // 选中创建的资源
            Selection.activeObject = database;
            EditorGUIUtility.PingObject(database);

            Debug.Log($"技能数据库已创建: {fullPath}");
        }

        private void CreateAndInitializeDatabase()
        {
            CreateSkillDatabase();

            // 查找刚创建的数据库
            string fullPath = Path.Combine(savePath, databaseName + ".asset");
            SkillDatabase database = AssetDatabase.LoadAssetAtPath<SkillDatabase>(fullPath);

            if (database != null)
            {
                InitializeDefaultSkills(database);
                EditorUtility.SetDirty(database);
                AssetDatabase.SaveAssets();
                Debug.Log("默认技能已添加到数据库");
            }
        }

        private void InitializeDefaultSkills(SkillDatabase database)
        {
            database.ClearDatabase();

            // BR01 - 获得力量(1)
            database.AddSkill(new SkillData
            {
                skillId = "BR01",
                skillName = "力量觉醒",
                description = "获得力量(1)",
                triggerType = SkillTriggerType.OnLand,
                effectType = SkillEffectType.Buff,
                baseValue = 1
            });

            // BR02 - Deal x damage to target; Instinct: +1 damage
            database.AddSkill(new SkillData
            {
                skillId = "BR02",
                skillName = "Precise Strike",
                description = "Deal x damage to target; Instinct: +1 damage",
                triggerType = SkillTriggerType.OnClear,
                effectType = SkillEffectType.Attack,
                targetType = TargetType.Current,
                baseValue = 1,
                scalesWithClear = true,
                requiresInstinct = false
            });

            // BR03 - Deal 1 damage to target
            database.AddSkill(new SkillData
            {
                skillId = "BR03",
                skillName = "Quick Attack",
                description = "Deal 1 damage to target",
                triggerType = SkillTriggerType.OnClear,
                effectType = SkillEffectType.Attack,
                targetType = TargetType.Current,
                baseValue = 1
            });

            // BR05 - Gain Shield(x)
            database.AddSkill(new SkillData
            {
                skillId = "BR05",
                skillName = "Defensive Stance",
                description = "Gain Shield(x)",
                triggerType = SkillTriggerType.OnLand,
                effectType = SkillEffectType.Shield,
                baseValue = 1,
                scalesWithClear = true
            });

            // BR07 - 获得力量(1)；下落速度(3)：获得力量(1)
            database.AddSkill(new SkillData
            {
                skillId = "BR07",
                skillName = "速度力量",
                description = "获得力量(1)；下落速度(3)：获得力量(1)",
                triggerType = SkillTriggerType.OnLand,
                effectType = SkillEffectType.Buff,
                baseValue = 1,
                speedThreshold = 3f
            });

            // BR12 - 特殊计数器技能
            database.AddSkill(new SkillData
            {
                skillId = "BR12",
                skillName = "蓄力",
                description = "增加计数器",
                triggerType = SkillTriggerType.OnLand,
                effectType = SkillEffectType.Special,
                baseValue = 4
            });

            // BR13 - 下落速度(10)：获得行动次数(1)；直觉
            database.AddSkill(new SkillData
            {
                skillId = "BR13",
                skillName = "高速直觉",
                description = "下落速度(10)：获得行动次数(1)；直觉",
                triggerType = SkillTriggerType.OnLand,
                effectType = SkillEffectType.Special,
                baseValue = 1,
                speedThreshold = 10f
            });

            // BR14 - 对目标造成2点伤害；下落速度(8)：获得力量(1)
            database.AddSkill(new SkillData
            {
                skillId = "BR14",
                skillName = "冲击打击",
                description = "对目标造成2点伤害；下落速度(8)：获得力量(1)",
                triggerType = SkillTriggerType.OnLand,
                effectType = SkillEffectType.Attack,
                baseValue = 2,
                speedThreshold = 8f
            });

            // BR17 - Placeholder skill
            database.AddSkill(new SkillData
            {
                skillId = "BR17",
                skillName = "Reserved Skill",
                description = "Reserved skill slot",
                triggerType = SkillTriggerType.OnLand,
                effectType = SkillEffectType.Special,
                baseValue = 0
            });

            // Add target selection examples if requested
            if (includeTargetExamples)
            {
                // Front target attack
                database.AddSkill(new SkillData
                {
                    skillId = "ATTACK_FRONT",
                    skillName = "Front Assault",
                    description = "Attack front enemy with scaling damage",
                    triggerType = SkillTriggerType.OnClear,
                    effectType = SkillEffectType.Attack,
                    targetType = TargetType.Front,
                    baseValue = 3,
                    scalesWithClear = true
                });

                // Back target attack
                database.AddSkill(new SkillData
                {
                    skillId = "ATTACK_BACK",
                    skillName = "Back Snipe",
                    description = "Attack back enemy with high damage",
                    triggerType = SkillTriggerType.OnClear,
                    effectType = SkillEffectType.Attack,
                    targetType = TargetType.Back,
                    baseValue = 4,
                    scalesWithClear = true
                });

                // Random target attack
                database.AddSkill(new SkillData
                {
                    skillId = "ATTACK_RANDOM",
                    skillName = "Random Strike",
                    description = "Attack random enemy",
                    triggerType = SkillTriggerType.OnClear,
                    effectType = SkillEffectType.Attack,
                    targetType = TargetType.Random,
                    baseValue = 2,
                    scalesWithClear = true
                });

                // All targets attack
                database.AddSkill(new SkillData
                {
                    skillId = "ATTACK_ALL",
                    skillName = "Area Attack",
                    description = "Attack all enemies with reduced damage",
                    triggerType = SkillTriggerType.OnClear,
                    effectType = SkillEffectType.Attack,
                    targetType = TargetType.All,
                    baseValue = 1,
                    scalesWithClear = true
                });
            }
        }
    }
}
#endif
