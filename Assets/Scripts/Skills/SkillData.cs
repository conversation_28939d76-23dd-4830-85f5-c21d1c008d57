using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace EOP.Skills
{
    [System.Serializable]
    public enum SkillTriggerType
    {
        OnLand,      // 落地时触发
        OnClear,     // 消除时触发
        Persistent   // 持续效果
    }

    [System.Serializable]
    public enum SkillEffectType
    {
        Attack,      // 攻击
        Heal,        // 治疗
        Shield,      // 护盾
        Buff,        // 增益
        Debuff,      // 减益
        Speed,       // 速度
        Special      // 特殊效果
    }

    [System.Serializable]
    public enum TargetType
    {
        Front,       // 前排敌人
        Back,        // 后排敌人
        Random,      // 随机敌人
        All,         // 所有敌人
        Self         // 自己（治疗、护盾等）
    }

    [System.Serializable]
    public class SkillData
    {
        [Header("基本信息")]
        public string skillId;
        public string skillName;
        public string description;
        
        [Header("触发条件")]
        public List<SkillTriggerType> triggerTypes = new List<SkillTriggerType> { SkillTriggerType.OnLand };
        public int requiredClearCount = 1; // 需要消除的方块数量
        
        [Header("效果类型")]
        public SkillEffectType effectType;

        [Header("目标选择")]
        public TargetType targetType = TargetType.Self;  // 目标类型
        public int randomTargetCount = 1;  // 随机目标数量（仅当targetType为Random时有效）

        [Header("效果数值")]
        public int baseValue;           // 基础数值
        public float speedMultiplier;   // 速度倍数影响
        public bool scalesWithClear;    // 是否随消除数量缩放
        
        [Header("特殊条件")]
        public float speedThreshold;    // 速度阈值
        public bool requiresInstinct;   // 是否需要直觉状态
        
        [Header("持续效果")]
        public int duration;            // 持续时间（回合数）
        public bool stackable;          // 是否可叠加

        public SkillData()
        {
            skillId = "";
            skillName = "";
            description = "";
            triggerTypes = new List<SkillTriggerType> { SkillTriggerType.OnLand };
            effectType = SkillEffectType.Attack;
            targetType = TargetType.Self;
            randomTargetCount = 1;
            baseValue = 1;
            speedMultiplier = 1f;
            scalesWithClear = false;
            speedThreshold = 0f;
            requiresInstinct = false;
            duration = 0;
            stackable = false;
        }

        public SkillData(string id, string name, string desc, SkillTriggerType trigger, SkillEffectType effect, int value)
        {
            skillId = id;
            skillName = name;
            description = desc;
            triggerTypes = new List<SkillTriggerType> { trigger };
            effectType = effect;
            targetType = TargetType.Self;
            randomTargetCount = 1;
            baseValue = value;
            speedMultiplier = 1f;
            scalesWithClear = false;
            speedThreshold = 0f;
            requiresInstinct = false;
            duration = 0;
            stackable = false;
        }

        /// <summary>
        /// 复制技能数据
        /// </summary>
        public SkillData Clone()
        {
            SkillData clone = new SkillData();
            clone.skillId = this.skillId;
            clone.skillName = this.skillName;
            clone.description = this.description;
            clone.triggerTypes = new List<SkillTriggerType>(this.triggerTypes);
            clone.requiredClearCount = this.requiredClearCount;
            clone.effectType = this.effectType;
            clone.targetType = this.targetType;
            clone.randomTargetCount = this.randomTargetCount;
            clone.baseValue = this.baseValue;
            clone.speedMultiplier = this.speedMultiplier;
            clone.scalesWithClear = this.scalesWithClear;
            clone.speedThreshold = this.speedThreshold;
            clone.requiresInstinct = this.requiresInstinct;
            clone.duration = this.duration;
            clone.stackable = this.stackable;
            return clone;
        }

        /// <summary>
        /// 验证技能数据是否有效
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(skillId) && !string.IsNullOrEmpty(skillName);
        }

        /// <summary>
        /// 获取技能的详细描述
        /// </summary>
        public string GetDetailedDescription()
        {
            string details = description;
            
            if (speedThreshold > 0)
            {
                details += $"\n需要速度: {speedThreshold}";
            }
            
            if (requiresInstinct)
            {
                details += "\n需要直觉状态";
            }
            
            if (scalesWithClear)
            {
                details += "\n效果随消除数量增加";
            }
            
            return details;
        }

        /// <summary>
        /// 获取技能类型的显示文本
        /// </summary>
        public string GetTriggerTypeText()
        {
            if (triggerTypes == null || triggerTypes.Count == 0)
                return "未知";

            if (triggerTypes.Count == 1)
            {
                switch (triggerTypes[0])
                {
                    case SkillTriggerType.OnLand:
                        return "落地触发";
                    case SkillTriggerType.OnClear:
                        return "消除触发";
                    case SkillTriggerType.Persistent:
                        return "持续效果";
                    default:
                        return "未知";
                }
            }
            else
            {
                var triggerTexts = new List<string>();
                foreach (var trigger in triggerTypes)
                {
                    switch (trigger)
                    {
                        case SkillTriggerType.OnLand:
                            triggerTexts.Add("落地");
                            break;
                        case SkillTriggerType.OnClear:
                            triggerTexts.Add("消除");
                            break;
                        case SkillTriggerType.Persistent:
                            triggerTexts.Add("持续");
                            break;
                    }
                }
                return string.Join(" + ", triggerTexts) + "触发";
            }
        }

        /// <summary>
        /// 获取效果类型的显示文本
        /// </summary>
        public string GetEffectTypeText()
        {
            switch (effectType)
            {
                case SkillEffectType.Attack:
                    return "攻击";
                case SkillEffectType.Heal:
                    return "治疗";
                case SkillEffectType.Shield:
                    return "护盾";
                case SkillEffectType.Buff:
                    return "增益";
                case SkillEffectType.Debuff:
                    return "减益";
                case SkillEffectType.Speed:
                    return "速度";
                case SkillEffectType.Special:
                    return "特殊";
                default:
                    return "未知";
            }
        }

        /// <summary>
        /// 获取目标类型的显示文本
        /// </summary>
        public string GetTargetTypeText()
        {
            switch (targetType)
            {
                case TargetType.Front:
                    return "前排敌人";
                case TargetType.Back:
                    return "后排敌人";
                case TargetType.Random:
                    return randomTargetCount > 1 ? $"随机{randomTargetCount}个敌人" : "随机敌人";
                case TargetType.All:
                    return "所有敌人";
                case TargetType.Self:
                    return "自己";
                default:
                    return "未知";
            }
        }

        /// <summary>
        /// 检查是否包含指定的触发类型
        /// </summary>
        public bool HasTriggerType(SkillTriggerType triggerType)
        {
            return triggerTypes != null && triggerTypes.Contains(triggerType);
        }

        /// <summary>
        /// 添加触发类型
        /// </summary>
        public void AddTriggerType(SkillTriggerType triggerType)
        {
            if (triggerTypes == null)
                triggerTypes = new List<SkillTriggerType>();

            if (!triggerTypes.Contains(triggerType))
                triggerTypes.Add(triggerType);
        }

        /// <summary>
        /// 移除触发类型
        /// </summary>
        public void RemoveTriggerType(SkillTriggerType triggerType)
        {
            if (triggerTypes != null)
                triggerTypes.Remove(triggerType);
        }
    }
}
