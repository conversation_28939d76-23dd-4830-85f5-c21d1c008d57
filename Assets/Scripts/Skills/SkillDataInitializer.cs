using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace EOP.Skills
{
    public class SkillDataInitializer : MonoBehaviour
    {
        [Header("技能数据库")]
        [SerializeField] private SkillDatabase skillDatabase;

        [ContextMenu("Initialize Skill Database")]
        public void InitializeSkillDatabase()
        {
            if (skillDatabase == null)
            {
                Debug.LogError("SkillDatabase 未设置");
                return;
            }

            skillDatabase.skills.Clear();
            
            // 根据表格添加技能数据
            AddBareHandSkills();
            
            Debug.Log("技能数据库初始化完成");
        }

        private void AddBareHandSkills()
        {
            // BR01 - 获得力量(1)
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "BR01",
                skillName = "力量觉醒",
                description = "获得力量(1)",
                triggerTypes = new List<SkillTriggerType> { SkillTriggerType.OnLand },
                effectType = SkillEffectType.Buff,
                targetType = TargetType.Self,
                baseValue = 1
            });

            // BR02 - 对目标造成x点伤害；直觉：造成的伤害+1
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "BR02",
                skillName = "精准打击",
                description = "对目标造成x点伤害；直觉：造成的伤害+1",
                triggerType = SkillTriggerType.OnClear,
                effectType = SkillEffectType.Attack,
                targetType = TargetType.Current,
                baseValue = 1,
                scalesWithClear = true,
                requiresInstinct = false
            });

            // BR03 - 对目标造成1点伤害
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "BR03",
                skillName = "快速攻击",
                description = "对目标造成1点伤害",
                triggerType = SkillTriggerType.OnClear,
                effectType = SkillEffectType.Attack,
                targetType = TargetType.Current,
                baseValue = 1
            });

            // BR05 - 获得护盾(x)
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "BR05",
                skillName = "防御姿态",
                description = "获得护盾(x)",
                triggerType = SkillTriggerType.OnLand,
                effectType = SkillEffectType.Shield,
                baseValue = 1,
                scalesWithClear = true
            });

            // BR07 - 获得力量(1)；下落速度(3)：获得力量(1)
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "BR07",
                skillName = "速度力量",
                description = "获得力量(1)；下落速度(3)：获得力量(1)",
                triggerType = SkillTriggerType.OnLand,
                effectType = SkillEffectType.Buff,
                baseValue = 1,
                speedThreshold = 3f
            });

            // BR12 - 特殊计数器技能
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "BR12",
                skillName = "蓄力",
                description = "增加计数器",
                triggerType = SkillTriggerType.OnLand,
                effectType = SkillEffectType.Special,
                baseValue = 4
            });

            // BR13 - 下落速度(10)：获得行动次数(1)；直觉
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "BR13",
                skillName = "高速直觉",
                description = "下落速度(10)：获得行动次数(1)；直觉",
                triggerType = SkillTriggerType.OnLand,
                effectType = SkillEffectType.Special,
                baseValue = 1,
                speedThreshold = 10f
            });

            // BR14 - 对目标造成2点伤害；下落速度(8)：获得力量(1)
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "BR14",
                skillName = "冲击打击",
                description = "对目标造成2点伤害；下落速度(8)：获得力量(1)",
                triggerType = SkillTriggerType.OnLand,
                effectType = SkillEffectType.Attack,
                targetType = TargetType.Current,
                baseValue = 2,
                speedThreshold = 8f
            });

            // BR17 - 占位符技能
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "BR17",
                skillName = "预留技能",
                description = "预留技能位",
                triggerType = SkillTriggerType.OnLand,
                effectType = SkillEffectType.Special,
                baseValue = 0
            });

            // 添加更多技能...
            AddAdditionalSkills();
        }

        private void AddAdditionalSkills()
        {
            // 可以在这里添加更多技能
            // 例如其他武器的技能或特殊技能

            // 示例：治疗技能
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "HEAL01",
                skillName = "生命恢复",
                description = "恢复x点生命值",
                triggerType = SkillTriggerType.OnClear,
                effectType = SkillEffectType.Heal,
                baseValue = 2,
                scalesWithClear = true
            });

            // 示例：速度提升技能
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "SPEED01",
                skillName = "加速",
                description = "提升下落速度",
                triggerType = SkillTriggerType.OnLand,
                effectType = SkillEffectType.Speed,
                baseValue = 2
            });

            // 示例：前排攻击技能
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "ATTACK_FRONT",
                skillName = "前排突击",
                description = "攻击前排敌人",
                triggerType = SkillTriggerType.OnClear,
                effectType = SkillEffectType.Attack,
                targetType = TargetType.Front,
                baseValue = 3,
                scalesWithClear = true
            });

            // 示例：后排攻击技能
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "ATTACK_BACK",
                skillName = "后排狙击",
                description = "攻击后排敌人",
                triggerType = SkillTriggerType.OnClear,
                effectType = SkillEffectType.Attack,
                targetType = TargetType.Back,
                baseValue = 4,
                scalesWithClear = true
            });

            // 示例：随机攻击技能
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "ATTACK_RANDOM",
                skillName = "随机打击",
                description = "攻击随机敌人",
                triggerType = SkillTriggerType.OnClear,
                effectType = SkillEffectType.Attack,
                targetType = TargetType.Random,
                baseValue = 2,
                scalesWithClear = true
            });

            // 示例：群体攻击技能
            skillDatabase.skills.Add(new SkillData
            {
                skillId = "ATTACK_ALL",
                skillName = "群体攻击",
                description = "攻击所有敌人",
                triggerType = SkillTriggerType.OnClear,
                effectType = SkillEffectType.Attack,
                targetType = TargetType.All,
                baseValue = 1,
                scalesWithClear = true
            });
        }

        private void OnValidate()
        {
            if (skillDatabase != null && Application.isPlaying)
            {
                // 在编辑器中验证数据
            }
        }
    }
}
