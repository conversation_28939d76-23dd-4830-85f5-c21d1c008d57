# Skill System Editor Documentation

## Overview
The Skill System includes custom editors to make skill creation and management easier. All editors are in English with detailed field descriptions.

## Skill Database Editor

### Accessing the Editor
1. Create a SkillDatabase ScriptableObject: `Create > Skills > Skill Database`
2. Select the SkillDatabase asset in the Project window
3. The custom editor will appear in the Inspector

### Editor Features

#### Header Controls
- **Show Field Descriptions**: Toggle to show/hide helpful tooltips for each field
- **Add New Skill**: Creates a new skill with default values
- **Clear All Skills**: Removes all skills from the database (with confirmation)
- **Initialize Default Skills**: Adds all BR series skills to the database

#### Skill Statistics
Displays a breakdown of skills by:
- **Trigger Types**: OnLand, OnClear, Persistent
- **Effect Types**: Attack, Buff, Heal, Shield, Special
- **Target Types**: Front, Back, Random, All, Current (for attack skills)

#### Individual Skill Editing

##### Basic Information
- **Skill ID**: Unique identifier used in code (e.g., "BR01", "ATTACK_FRONT")
- **Skill Name**: Display name shown to players
- **Description**: Detailed explanation of what the skill does

##### Trigger & Effect
- **Trigger Type**: When the skill activates
  - `OnLand`: When a tetris piece lands
  - `OnClear`: When lines are cleared
  - `Persistent`: Continuous effect over time
- **Effect Type**: What the skill does
  - `Attack`: Damage enemies
  - `Heal`: Restore player HP
  - `Shield`: Add protection
  - `Buff`: Enhance player abilities
  - `Special`: Custom effects
- **Target Type**: Which enemies to target (only for Attack skills)
  - `Front`: Front enemy
  - `Back`: Back enemy
  - `Random`: Random enemy
  - `All`: All enemies
  - `Current`: Currently selected target

##### Values & Scaling
- **Base Value**: Base effect strength before modifiers
- **Speed Multiplier**: How player speed affects the skill (1.0 = no influence)
- **Scales With Clear Count**: Effect multiplies by number of cleared blocks

##### Conditions
- **Speed Threshold**: Minimum player speed required (0 = no requirement)
- **Requires Instinct**: Skill only works in instinct state

##### Persistent Settings (only for Persistent skills)
- **Duration**: Number of turns effect lasts (0 = permanent)
- **Stackable**: Whether multiple instances can be active

## Player Deck Data Editor

### Accessing the Editor
1. Select a GameObject with a BasicDecoder component
2. The custom editor will appear in the Inspector under "Player Deck Data Management"

### Editor Features

#### Deck Statistics
- **Entry Count**: Number of piece-to-skill mappings
- **Dictionary Count**: Number of entries in the runtime dictionary

#### Adding Entries
- **Key**: Piece identifier (e.g., "RedL", "BlueT", "OrangeI")
- **Value**: Skill ID (e.g., "BR01", "BR02", "ATTACK_FRONT")

#### Existing Entries
- Shows all current piece-to-skill mappings
- Each entry displays: Index, Key → Value, Delete button
- Hover over fields to see tooltips

#### Utility Tools
- **Validate Data**: Check for errors and duplicate keys
- **Reset to Default**: Restore original BR series mappings
- **Clear Data**: Remove all entries
- **Sync List → Dict**: Update runtime dictionary from serialized list
- **Sync Dict → List**: Update serialized list from runtime dictionary
- **Print Info**: Log deck information to console

## Skill Database Creator Window

### Accessing the Creator
1. Go to `Tools > Skills > Create Skill Database` in the menu bar
2. The Skill Database Creator window will open

### Creator Features
- **Database Name**: Name for the new database file
- **Save Path**: Directory where the database will be saved
- **Include Target Examples**: Add example skills with different target types
- **Create Skill Database**: Creates empty database
- **Create and Initialize Default Skills**: Creates database with all default skills

## Best Practices

### Skill ID Naming
- Use consistent prefixes (e.g., "BR" for bare hand skills)
- Include numbers for variations (e.g., "BR01", "BR02")
- Use descriptive names for special skills (e.g., "ATTACK_FRONT", "HEAL_ALL")

### Target Type Usage
- **Current**: Most common, attacks selected target
- **Front/Back**: Strategic targeting for specific enemy positions
- **Random**: Adds unpredictability to combat
- **All**: Powerful but usually with reduced damage per target

### Scaling Guidelines
- **Scales With Clear Count**: Good for rewarding large clears
- **Speed Multiplier**: Rewards fast play, use sparingly
- **Speed Threshold**: Creates skill requirements and progression

### Testing Skills
1. Use the TargetSelectionTest component for testing target types
2. Use the SkillSystemTest component for general skill testing
3. Always validate data before building

## Troubleshooting

### Common Issues
1. **Skills not appearing**: Check if SkillDatabase is assigned to SkillManager
2. **Target selection not working**: Ensure TargetSelector component exists
3. **Data not syncing**: Use "Sync List → Dict" button in editor
4. **Validation errors**: Check for duplicate IDs or empty fields

### Debug Tools
- Enable "Show Field Descriptions" for detailed tooltips
- Use "Print Info" to log current deck state
- Check Console for validation messages
- Use Context Menu options on components for quick testing

## Integration Notes

### With Existing Systems
- The editors automatically integrate with existing CombatManager
- Target selection works with existing enemy targeting methods
- Skill effects use existing buff/damage systems

### Performance Considerations
- Validation runs automatically in editor, not at runtime
- Dictionary lookups are cached for performance
- Target selection preserves original target after attacks

## Future Extensions

### Adding New Effect Types
1. Add new enum value to SkillEffectType
2. Update skill effect execution in SkillManager
3. Add handling in appropriate SkillEffect classes
4. Update editor descriptions

### Adding New Target Types
1. Add new enum value to TargetType
2. Implement targeting logic in TargetSelector
3. Update editor descriptions and help text
4. Test with existing skills
