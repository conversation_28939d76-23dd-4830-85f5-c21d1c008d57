#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using EOP.Skills;

namespace EOP.Skills.Editor
{
    [CustomEditor(typeof(SkillDatabase))]
    public class SkillDatabaseEditor : UnityEditor.Editor
    {
        private SkillDatabase database;
        private Vector2 scrollPosition;
        private bool showHelp = false;

        private void OnEnable()
        {
            database = (SkillDatabase)target;
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            EditorGUILayout.LabelField("Skill Database Editor", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // Help toggle
            showHelp = EditorGUILayout.Toggle("Show Field Descriptions", showHelp);
            EditorGUILayout.Space();

            // Quick action buttons
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Add New Skill"))
            {
                AddNewSkill();
            }
            if (GUILayout.Button("Clear All Skills"))
            {
                if (EditorUtility.DisplayDialog("Confirm", "Are you sure you want to clear all skills?", "Yes", "Cancel"))
                {
                    ClearAllSkills();
                }
            }
            if (GUILayout.But<PERSON>("Initialize Default Skills"))
            {
                InitializeDefaultSkills();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            // Skill statistics
            EditorGUILayout.LabelField($"Total Skills: {database.skills.Count}", EditorStyles.helpBox);

            // Show statistics breakdown
            if (database.skills.Count > 0)
            {
                var stats = GetSkillStatistics();
                EditorGUILayout.LabelField(stats, EditorStyles.helpBox);
            }

            EditorGUILayout.Space();

            // Skill list
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            for (int i = 0; i < database.skills.Count; i++)
            {
                DrawSkillEditor(i);
                EditorGUILayout.Space();
            }

            EditorGUILayout.EndScrollView();

            serializedObject.ApplyModifiedProperties();

            if (GUI.changed)
            {
                EditorUtility.SetDirty(database);
            }
        }

        private void DrawSkillEditor(int index)
        {
            SkillData skill = database.skills[index];

            EditorGUILayout.BeginVertical("box");

            // Skill header and delete button
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"Skill {index + 1}: {skill.skillId}", EditorStyles.boldLabel);
            if (GUILayout.Button("Delete", GUILayout.Width(60)))
            {
                if (EditorUtility.DisplayDialog("Confirm Delete", $"Are you sure you want to delete skill {skill.skillId}?", "Yes", "Cancel"))
                {
                    database.skills.RemoveAt(index);
                    return;
                }
            }
            EditorGUILayout.EndHorizontal();

            // Basic Information
            EditorGUILayout.LabelField("Basic Information", EditorStyles.boldLabel);

            skill.skillId = EditorGUILayout.TextField(new GUIContent("Skill ID", "Unique identifier for this skill"), skill.skillId);
            if (showHelp) EditorGUILayout.HelpBox("Unique string identifier used to reference this skill in code", MessageType.Info);

            skill.skillName = EditorGUILayout.TextField(new GUIContent("Skill Name", "Display name shown to players"), skill.skillName);
            if (showHelp) EditorGUILayout.HelpBox("Human-readable name displayed in UI", MessageType.Info);

            EditorGUILayout.LabelField("Description:");
            skill.description = EditorGUILayout.TextArea(skill.description, GUILayout.Height(40));
            if (showHelp) EditorGUILayout.HelpBox("Detailed description of what this skill does", MessageType.Info);

            EditorGUILayout.Space();

            // Trigger and Effect Types
            EditorGUILayout.LabelField("Trigger & Effect", EditorStyles.boldLabel);

            // Multiple Trigger Types
            EditorGUILayout.LabelField(new GUIContent("Trigger Types", "When this skill activates (can have multiple)"));
            if (skill.triggerTypes == null) skill.triggerTypes = new List<SkillTriggerType> { SkillTriggerType.OnLand };

            for (int i = 0; i < skill.triggerTypes.Count; i++)
            {
                EditorGUILayout.BeginHorizontal();
                skill.triggerTypes[i] = (SkillTriggerType)EditorGUILayout.EnumPopup($"Trigger {i + 1}", skill.triggerTypes[i]);
                if (GUILayout.Button("Remove", GUILayout.Width(60)) && skill.triggerTypes.Count > 1)
                {
                    skill.triggerTypes.RemoveAt(i);
                    break;
                }
                EditorGUILayout.EndHorizontal();
            }

            if (GUILayout.Button("Add Trigger Type"))
            {
                skill.triggerTypes.Add(SkillTriggerType.OnLand);
            }

            if (showHelp) EditorGUILayout.HelpBox("OnLand: When piece lands | OnClear: When lines are cleared | Persistent: Continuous effect\nMultiple triggers mean the skill can activate in different situations", MessageType.Info);

            skill.effectType = (SkillEffectType)EditorGUILayout.EnumPopup(new GUIContent("Effect Type", "What this skill does"), skill.effectType);
            if (showHelp) EditorGUILayout.HelpBox("Attack: Damage enemies | Heal: Restore HP | Shield: Add protection | Buff: Enhance abilities | Special: Custom effects", MessageType.Info);

            // Target Selection (always show now)
            skill.targetType = (TargetType)EditorGUILayout.EnumPopup(new GUIContent("Target Type", "Who receives the effect"), skill.targetType);
            if (showHelp) EditorGUILayout.HelpBox("Front: Front enemy | Back: Back enemy | Random: Random enemy(s) | All: All enemies | Self: Player (for heals/buffs)", MessageType.Info);

            // Random Target Count (only for Random target type)
            if (skill.targetType == TargetType.Random)
            {
                skill.randomTargetCount = EditorGUILayout.IntField(new GUIContent("Random Target Count", "How many random targets to affect"), Mathf.Max(1, skill.randomTargetCount));
                if (showHelp) EditorGUILayout.HelpBox("Number of random enemies to target. Must be at least 1.", MessageType.Info);
            }

            EditorGUILayout.Space();

            // Numerical Values
            EditorGUILayout.LabelField("Values & Scaling", EditorStyles.boldLabel);

            skill.baseValue = EditorGUILayout.IntField(new GUIContent("Base Value", "Base effect strength"), skill.baseValue);
            if (showHelp) EditorGUILayout.HelpBox("Base damage/healing/shield amount before any modifiers", MessageType.Info);

            skill.speedMultiplier = EditorGUILayout.FloatField(new GUIContent("Speed Multiplier", "How player speed affects this skill"), skill.speedMultiplier);
            if (showHelp) EditorGUILayout.HelpBox("Multiplier applied to player speed when calculating effect. 1.0 = no speed influence", MessageType.Info);

            skill.scalesWithClear = EditorGUILayout.Toggle(new GUIContent("Scales With Clear Count", "Effect increases with cleared blocks"), skill.scalesWithClear);
            if (showHelp) EditorGUILayout.HelpBox("When enabled, effect strength multiplies by number of cleared blocks", MessageType.Info);

            EditorGUILayout.Space();

            // Special Conditions
            EditorGUILayout.LabelField("Conditions", EditorStyles.boldLabel);

            skill.speedThreshold = EditorGUILayout.FloatField(new GUIContent("Speed Threshold", "Minimum speed required"), skill.speedThreshold);
            if (showHelp) EditorGUILayout.HelpBox("Skill only activates if player speed is at least this value. 0 = no requirement", MessageType.Info);

            skill.requiresInstinct = EditorGUILayout.Toggle(new GUIContent("Requires Instinct", "Must be in instinct state"), skill.requiresInstinct);
            if (showHelp) EditorGUILayout.HelpBox("Skill only works when player is in special instinct state", MessageType.Info);

            // Persistent Effects (only for persistent skills)
            if (skill.triggerType == SkillTriggerType.Persistent)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Persistent Settings", EditorStyles.boldLabel);

                skill.duration = EditorGUILayout.IntField(new GUIContent("Duration", "How many turns effect lasts"), skill.duration);
                if (showHelp) EditorGUILayout.HelpBox("Number of turns this effect remains active. 0 = permanent", MessageType.Info);

                skill.stackable = EditorGUILayout.Toggle(new GUIContent("Stackable", "Can multiple instances exist"), skill.stackable);
                if (showHelp) EditorGUILayout.HelpBox("Whether multiple copies of this effect can be active simultaneously", MessageType.Info);
            }

            EditorGUILayout.EndVertical();
        }

        private void AddNewSkill()
        {
            SkillData newSkill = new SkillData();
            newSkill.skillId = $"NEW_{database.skills.Count + 1:00}";
            newSkill.skillName = "New Skill";
            newSkill.description = "Skill description";

            database.skills.Add(newSkill);
            EditorUtility.SetDirty(database);
        }

        private void ClearAllSkills()
        {
            database.skills.Clear();
            EditorUtility.SetDirty(database);
        }

        private void InitializeDefaultSkills()
        {
            if (EditorUtility.DisplayDialog("Confirm", "This will clear existing skills and add default skills. Continue?", "Yes", "Cancel"))
            {
                // Find SkillDataInitializer and call initialization method
                SkillDataInitializer initializer = FindObjectOfType<SkillDataInitializer>();
                if (initializer != null)
                {
                    initializer.InitializeSkillDatabase();
                }
                else
                {
                    // Manually add some basic skills
                    database.skills.Clear();

                    database.skills.Add(new SkillData("BR01", "Strength Awakening", "Gain Strength(1)", SkillTriggerType.OnLand, SkillEffectType.Buff, 1));
                    database.skills.Add(new SkillData("BR02", "Precise Strike", "Deal x damage to target", SkillTriggerType.OnClear, SkillEffectType.Attack, 1));
                    database.skills.Add(new SkillData("BR03", "Quick Attack", "Deal 1 damage to target", SkillTriggerType.OnClear, SkillEffectType.Attack, 1));
                    database.skills.Add(new SkillData("BR05", "Defensive Stance", "Gain Shield(x)", SkillTriggerType.OnLand, SkillEffectType.Shield, 1));
                }

                EditorUtility.SetDirty(database);
            }
        }

        private string GetSkillStatistics()
        {
            if (database.skills.Count == 0) return "";

            int landSkills = 0, clearSkills = 0, persistentSkills = 0;
            int attackSkills = 0, buffSkills = 0, healSkills = 0, shieldSkills = 0, specialSkills = 0;
            int frontTarget = 0, backTarget = 0, randomTarget = 0, allTarget = 0, currentTarget = 0;

            foreach (var skill in database.skills)
            {
                // Count trigger types
                switch (skill.triggerType)
                {
                    case SkillTriggerType.OnLand: landSkills++; break;
                    case SkillTriggerType.OnClear: clearSkills++; break;
                    case SkillTriggerType.Persistent: persistentSkills++; break;
                }

                // Count effect types
                switch (skill.effectType)
                {
                    case SkillEffectType.Attack: attackSkills++; break;
                    case SkillEffectType.Buff: buffSkills++; break;
                    case SkillEffectType.Heal: healSkills++; break;
                    case SkillEffectType.Shield: shieldSkills++; break;
                    case SkillEffectType.Special: specialSkills++; break;
                }

                // Count target types (only for attack skills)
                if (skill.effectType == SkillEffectType.Attack)
                {
                    switch (skill.targetType)
                    {
                        case TargetType.Front: frontTarget++; break;
                        case TargetType.Back: backTarget++; break;
                        case TargetType.Random: randomTarget++; break;
                        case TargetType.All: allTarget++; break;
                        case TargetType.Current: currentTarget++; break;
                    }
                }
            }

            return $"Triggers - Land: {landSkills}, Clear: {clearSkills}, Persistent: {persistentSkills}\n" +
                   $"Effects - Attack: {attackSkills}, Buff: {buffSkills}, Heal: {healSkills}, Shield: {shieldSkills}, Special: {specialSkills}\n" +
                   $"Targets - Front: {frontTarget}, Back: {backTarget}, Random: {randomTarget}, All: {allTarget}, Current: {currentTarget}";
        }
    }

    [CustomPropertyDrawer(typeof(SkillData))]
    public class SkillDataPropertyDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            // 计算各个字段的位置
            float lineHeight = EditorGUIUtility.singleLineHeight;
            float spacing = EditorGUIUtility.standardVerticalSpacing;
            
            Rect skillIdRect = new Rect(position.x, position.y, position.width * 0.3f, lineHeight);
            Rect skillNameRect = new Rect(position.x + position.width * 0.35f, position.y, position.width * 0.65f, lineHeight);

            // 绘制技能ID和名称
            SerializedProperty skillIdProp = property.FindPropertyRelative("skillId");
            SerializedProperty skillNameProp = property.FindPropertyRelative("skillName");
            
            EditorGUI.PropertyField(skillIdRect, skillIdProp, GUIContent.none);
            EditorGUI.PropertyField(skillNameRect, skillNameProp, GUIContent.none);

            EditorGUI.EndProperty();
        }

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            return EditorGUIUtility.singleLineHeight;
        }
    }
}
#endif
