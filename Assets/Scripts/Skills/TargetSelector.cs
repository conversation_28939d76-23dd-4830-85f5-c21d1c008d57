using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using EOP.combat;

namespace EOP.Skills
{
    /// <summary>
    /// 目标选择器 - 根据目标类型选择攻击目标
    /// </summary>
    public class TargetSelector : MonoBehaviour
    {
        [Header("战斗系统引用")]
        private CombatManager combatManager;

        private void Start()
        {
            combatManager = FindObjectOfType<CombatManager>();
        }

        /// <summary>
        /// 根据目标类型执行效果
        /// </summary>
        /// <param name="skillData">技能数据</param>
        /// <param name="effectValue">效果值</param>
        public void ExecuteEffectByTargetType(SkillData skillData, int effectValue)
        {
            if (combatManager == null)
            {
                Debug.LogWarning("CombatManager 未找到");
                return;
            }

            switch (skillData.targetType)
            {
                case TargetType.Front:
                    ExecuteFrontTarget(skillData, effectValue);
                    break;

                case TargetType.Back:
                    ExecuteBackTarget(skillData, effectValue);
                    break;

                case TargetType.Random:
                    ExecuteRandomTargets(skillData, effectValue);
                    break;

                case TargetType.All:
                    ExecuteAllTargets(skillData, effectValue);
                    break;

                case TargetType.Self:
                    ExecuteSelfTarget(skillData, effectValue);
                    break;

                default:
                    Debug.LogWarning($"未知的目标类型: {skillData.targetType}");
                    ExecuteSelfTarget(skillData, effectValue);
                    break;
            }
        }

        /// <summary>
        /// 根据目标类型执行攻击（向后兼容）
        /// </summary>
        /// <param name="targetType">目标类型</param>
        /// <param name="damage">伤害值</param>
        public void ExecuteAttackByTargetType(TargetType targetType, int damage)
        {
            var tempSkillData = new SkillData();
            tempSkillData.targetType = targetType;
            tempSkillData.effectType = SkillEffectType.Attack;
            tempSkillData.randomTargetCount = 1;

            ExecuteEffectByTargetType(tempSkillData, damage);
        }

        /// <summary>
        /// 执行前排目标效果
        /// </summary>
        private void ExecuteFrontTarget(SkillData skillData, int effectValue)
        {
            if (skillData.effectType == SkillEffectType.Attack)
            {
                AttackFrontEnemy(effectValue);
            }
            else
            {
                Debug.LogWarning($"前排目标不支持效果类型: {skillData.effectType}");
            }
        }

        /// <summary>
        /// 执行后排目标效果
        /// </summary>
        private void ExecuteBackTarget(SkillData skillData, int effectValue)
        {
            if (skillData.effectType == SkillEffectType.Attack)
            {
                AttackBackEnemy(effectValue);
            }
            else
            {
                Debug.LogWarning($"后排目标不支持效果类型: {skillData.effectType}");
            }
        }

        /// <summary>
        /// 执行随机目标效果
        /// </summary>
        private void ExecuteRandomTargets(SkillData skillData, int effectValue)
        {
            if (skillData.effectType == SkillEffectType.Attack)
            {
                AttackRandomEnemies(effectValue, skillData.randomTargetCount);
            }
            else
            {
                Debug.LogWarning($"随机目标不支持效果类型: {skillData.effectType}");
            }
        }

        /// <summary>
        /// 执行全体目标效果
        /// </summary>
        private void ExecuteAllTargets(SkillData skillData, int effectValue)
        {
            if (skillData.effectType == SkillEffectType.Attack)
            {
                AttackAllEnemies(effectValue);
            }
            else
            {
                Debug.LogWarning($"全体目标不支持效果类型: {skillData.effectType}");
            }
        }

        /// <summary>
        /// 执行自身目标效果
        /// </summary>
        private void ExecuteSelfTarget(SkillData skillData, int effectValue)
        {
            switch (skillData.effectType)
            {
                case SkillEffectType.Heal:
                    combatManager.ChangePlayerHP(effectValue);
                    Debug.Log($"治疗自己，恢复 {effectValue} 点生命值");
                    break;

                case SkillEffectType.Shield:
                    combatManager.ChangePlayerShield(effectValue);
                    Debug.Log($"为自己添加 {effectValue} 点护盾");
                    break;

                case SkillEffectType.Speed:
                    combatManager.ChangePlayerSpeed(effectValue);
                    Debug.Log($"提升自己 {effectValue} 点速度");
                    break;

                case SkillEffectType.Buff:
                    // 这里可以添加具体的Buff逻辑
                    Debug.Log($"为自己添加Buff效果，强度: {effectValue}");
                    break;

                default:
                    Debug.LogWarning($"自身目标不支持效果类型: {skillData.effectType}");
                    break;
            }
        }

        /// <summary>
        /// 攻击前排敌人
        /// </summary>
        private void AttackFrontEnemy(int damage)
        {
            if (combatManager._combatEnemies.Count > 0)
            {
                var originalTarget = combatManager.target;
                combatManager.TargetingFrontEnemy();
                combatManager.PlayerAttackTargetEnemy(damage);
                combatManager.target = originalTarget; // 恢复原目标

                Debug.Log($"攻击前排敌人，造成 {damage} 点伤害");
            }
            else
            {
                Debug.Log("没有敌人可攻击");
            }
        }

        /// <summary>
        /// 攻击后排敌人
        /// </summary>
        private void AttackBackEnemy(int damage)
        {
            if (combatManager._combatEnemies.Count > 0)
            {
                var originalTarget = combatManager.target;
                combatManager.TargetingBackEnemy();
                combatManager.PlayerAttackTargetEnemy(damage);
                combatManager.target = originalTarget; // 恢复原目标
                
                Debug.Log($"攻击后排敌人，造成 {damage} 点伤害");
            }
            else
            {
                Debug.Log("没有敌人可攻击");
            }
        }

        /// <summary>
        /// 攻击随机敌人
        /// </summary>
        private void AttackRandomEnemy(int damage)
        {
            AttackRandomEnemies(damage, 1);
        }

        /// <summary>
        /// 攻击多个随机敌人
        /// </summary>
        private void AttackRandomEnemies(int damage, int targetCount)
        {
            if (combatManager._combatEnemies.Count == 0)
            {
                Debug.Log("没有敌人可攻击");
                return;
            }

            var originalTarget = combatManager.target;
            int actualTargetCount = Mathf.Min(targetCount, combatManager._combatEnemies.Count);

            // 创建敌人列表的副本用于随机选择
            var availableEnemies = new List<CombatEnemy>(combatManager._combatEnemies);
            var attackedEnemies = new List<CombatEnemy>();

            for (int i = 0; i < actualTargetCount; i++)
            {
                if (availableEnemies.Count == 0) break;

                // 随机选择一个敌人
                int randomIndex = Random.Range(0, availableEnemies.Count);
                var targetEnemy = availableEnemies[randomIndex];

                // 攻击选中的敌人
                combatManager.target = targetEnemy;
                combatManager.PlayerAttackTargetEnemy(damage);

                attackedEnemies.Add(targetEnemy);
                availableEnemies.RemoveAt(randomIndex);
            }

            combatManager.target = originalTarget; // 恢复原目标

            Debug.Log($"攻击了 {attackedEnemies.Count} 个随机敌人，每个造成 {damage} 点伤害");
        }

        /// <summary>
        /// 攻击所有敌人
        /// </summary>
        private void AttackAllEnemies(int damage)
        {
            if (combatManager._combatEnemies.Count > 0)
            {
                var originalTarget = combatManager.target;
                
                // 对每个敌人造成伤害
                foreach (var enemy in combatManager._combatEnemies)
                {
                    combatManager.target = enemy;
                    combatManager.PlayerAttackTargetEnemy(damage);
                }
                
                combatManager.target = originalTarget; // 恢复原目标
                
                Debug.Log($"攻击所有敌人，每个敌人受到 {damage} 点伤害");
            }
            else
            {
                Debug.Log("没有敌人可攻击");
            }
        }

        /// <summary>
        /// 攻击当前目标
        /// </summary>
        private void AttackCurrentTarget(int damage)
        {
            if (combatManager.target != null)
            {
                combatManager.PlayerAttackTargetEnemy(damage);
                Debug.Log($"攻击当前目标，造成 {damage} 点伤害");
            }
            else if (combatManager._combatEnemies.Count > 0)
            {
                // 如果没有当前目标，默认攻击第一个敌人
                combatManager.TargetingFrontEnemy();
                combatManager.PlayerAttackTargetEnemy(damage);
                Debug.Log($"没有当前目标，攻击第一个敌人，造成 {damage} 点伤害");
            }
            else
            {
                Debug.Log("没有敌人可攻击");
            }
        }

        /// <summary>
        /// 获取指定目标类型的敌人数量
        /// </summary>
        public int GetTargetCount(TargetType targetType)
        {
            if (combatManager == null || combatManager._combatEnemies.Count == 0)
                return 0;

            switch (targetType)
            {
                case TargetType.Front:
                case TargetType.Back:
                case TargetType.Random:
                case TargetType.Current:
                    return 1;
                    
                case TargetType.All:
                    return combatManager._combatEnemies.Count;
                    
                default:
                    return 0;
            }
        }

        /// <summary>
        /// 检查指定目标类型是否有效
        /// </summary>
        public bool IsTargetTypeValid(TargetType targetType)
        {
            if (combatManager == null)
                return false;

            switch (targetType)
            {
                case TargetType.Front:
                case TargetType.Back:
                case TargetType.Random:
                case TargetType.All:
                    return combatManager._combatEnemies.Count > 0;
                    
                case TargetType.Current:
                    return combatManager.target != null || combatManager._combatEnemies.Count > 0;
                    
                default:
                    return false;
            }
        }

        /// <summary>
        /// 获取目标类型的描述信息
        /// </summary>
        public string GetTargetDescription(TargetType targetType)
        {
            if (combatManager == null)
                return "战斗管理器未找到";

            int enemyCount = combatManager._combatEnemies.Count;
            
            switch (targetType)
            {
                case TargetType.Front:
                    return enemyCount > 0 ? "前排敌人" : "无敌人";
                    
                case TargetType.Back:
                    return enemyCount > 0 ? "后排敌人" : "无敌人";
                    
                case TargetType.Random:
                    return enemyCount > 0 ? "随机敌人" : "无敌人";
                    
                case TargetType.All:
                    return $"所有敌人 ({enemyCount}个)";
                    
                case TargetType.Current:
                    if (combatManager.target != null)
                        return "当前目标";
                    else if (enemyCount > 0)
                        return "默认目标";
                    else
                        return "无目标";
                        
                default:
                    return "未知目标";
            }
        }

        /// <summary>
        /// 预览攻击效果（不实际执行）
        /// </summary>
        public string PreviewAttack(TargetType targetType, int damage)
        {
            if (!IsTargetTypeValid(targetType))
            {
                return "无有效目标";
            }

            int targetCount = GetTargetCount(targetType);
            string targetDesc = GetTargetDescription(targetType);
            
            return $"将对 {targetDesc} 造成 {damage} 点伤害 (目标数: {targetCount})";
        }
    }
}
