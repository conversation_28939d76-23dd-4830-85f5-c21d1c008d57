using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using EOP.Skills;
using EOP.combat;

namespace EOP.Skills.Test
{
    public class EnhancedSkillTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private SkillManager skillManager;
        [SerializeField] private TargetSelector targetSelector;
        [SerializeField] private SkillDatabase skillDatabase;
        
        [Header("测试按键")]
        [SerializeField] private KeyCode testSelfTargetKey = KeyCode.F6;
        [SerializeField] private KeyCode testMultiRandomKey = KeyCode.F7;
        [SerializeField] private KeyCode testMultiTriggerKey = KeyCode.F8;
        [SerializeField] private KeyCode showSkillInfoKey = KeyCode.F9;

        private void Start()
        {
            // 查找系统组件
            if (skillManager == null)
                skillManager = FindObjectOfType<SkillManager>();
                
            if (targetSelector == null)
                targetSelector = FindObjectOfType<TargetSelector>();
                
            if (skillDatabase == null)
                skillDatabase = FindObjectOfType<SkillDatabase>();
        }

        private void Update()
        {
            CheckTestInputs();
        }

        private void CheckTestInputs()
        {
            // 测试自身目标技能
            if (Input.GetKeyDown(testSelfTargetKey))
            {
                TestSelfTargetSkill();
            }
            
            // 测试多随机目标技能
            if (Input.GetKeyDown(testMultiRandomKey))
            {
                TestMultiRandomTargetSkill();
            }
            
            // 测试多触发类型技能
            if (Input.GetKeyDown(testMultiTriggerKey))
            {
                TestMultiTriggerSkill();
            }
            
            // 显示技能信息
            if (Input.GetKeyDown(showSkillInfoKey))
            {
                ShowEnhancedSkillInfo();
            }
        }

        private void TestSelfTargetSkill()
        {
            Debug.Log("=== 测试自身目标技能 ===");
            
            // 创建一个治疗技能
            var healSkill = new SkillData
            {
                skillId = "TEST_HEAL",
                skillName = "测试治疗",
                description = "治疗自己",
                triggerTypes = new List<SkillTriggerType> { SkillTriggerType.OnClear },
                effectType = SkillEffectType.Heal,
                targetType = TargetType.Self,
                baseValue = 5,
                scalesWithClear = true
            };

            if (targetSelector != null)
            {
                targetSelector.ExecuteEffectByTargetType(healSkill, 5);
            }
        }

        private void TestMultiRandomTargetSkill()
        {
            Debug.Log("=== 测试多随机目标技能 ===");
            
            if (skillManager != null)
            {
                skillManager.ProcessClearSkill("MULTI_RANDOM", 2);
            }
            else
            {
                // 手动测试
                var multiRandomSkill = new SkillData
                {
                    skillId = "TEST_MULTI_RANDOM",
                    skillName = "测试多随机攻击",
                    description = "攻击3个随机敌人",
                    triggerTypes = new List<SkillTriggerType> { SkillTriggerType.OnClear },
                    effectType = SkillEffectType.Attack,
                    targetType = TargetType.Random,
                    randomTargetCount = 3,
                    baseValue = 2
                };

                if (targetSelector != null)
                {
                    targetSelector.ExecuteEffectByTargetType(multiRandomSkill, 2);
                }
            }
        }

        private void TestMultiTriggerSkill()
        {
            Debug.Log("=== 测试多触发类型技能 ===");
            
            if (skillManager != null)
            {
                // 测试落地触发
                skillManager.ProcessLandSkill("MULTI_TRIGGER", 1.0f);
                
                // 测试消除触发
                skillManager.ProcessClearSkill("MULTI_TRIGGER", 2);
            }
            else
            {
                Debug.LogWarning("SkillManager 未找到");
            }
        }

        private void ShowEnhancedSkillInfo()
        {
            Debug.Log("=== 增强技能系统信息 ===");
            
            if (skillDatabase != null)
            {
                foreach (var skill in skillDatabase.skills)
                {
                    if (skill.skillId.Contains("MULTI") || skill.targetType == TargetType.Self)
                    {
                        Debug.Log($"技能: {skill.skillName}");
                        Debug.Log($"  ID: {skill.skillId}");
                        Debug.Log($"  触发类型: {skill.GetTriggerTypeText()}");
                        Debug.Log($"  效果类型: {skill.GetEffectTypeText()}");
                        Debug.Log($"  目标类型: {skill.GetTargetTypeText()}");
                        
                        if (skill.targetType == TargetType.Random)
                        {
                            Debug.Log($"  随机目标数量: {skill.randomTargetCount}");
                        }
                        
                        if (skill.triggerTypes != null && skill.triggerTypes.Count > 1)
                        {
                            Debug.Log($"  多触发类型数量: {skill.triggerTypes.Count}");
                        }
                        
                        Debug.Log("---");
                    }
                }
            }
            else
            {
                Debug.LogWarning("SkillDatabase 未找到");
            }
        }

        [ContextMenu("创建测试技能")]
        public void CreateTestSkills()
        {
            if (skillDatabase == null)
            {
                Debug.LogWarning("SkillDatabase 未设置");
                return;
            }

            Debug.Log("创建增强功能测试技能...");

            // 自身护盾技能
            var shieldSkill = new SkillData
            {
                skillId = "TEST_SELF_SHIELD",
                skillName = "自我防护",
                description = "为自己添加护盾",
                triggerTypes = new List<SkillTriggerType> { SkillTriggerType.OnLand },
                effectType = SkillEffectType.Shield,
                targetType = TargetType.Self,
                baseValue = 3,
                scalesWithClear = false
            };

            // 多目标随机攻击
            var multiAttackSkill = new SkillData
            {
                skillId = "TEST_MULTI_ATTACK",
                skillName = "散射攻击",
                description = "攻击5个随机敌人",
                triggerTypes = new List<SkillTriggerType> { SkillTriggerType.OnClear },
                effectType = SkillEffectType.Attack,
                targetType = TargetType.Random,
                randomTargetCount = 5,
                baseValue = 1,
                scalesWithClear = true
            };

            // 多触发类型增益技能
            var multiTriggerBuff = new SkillData
            {
                skillId = "TEST_MULTI_BUFF",
                skillName = "全能增益",
                description = "落地、消除、持续都能触发的增益",
                triggerTypes = new List<SkillTriggerType> 
                { 
                    SkillTriggerType.OnLand, 
                    SkillTriggerType.OnClear, 
                    SkillTriggerType.Persistent 
                },
                effectType = SkillEffectType.Buff,
                targetType = TargetType.Self,
                baseValue = 1,
                duration = 3,
                stackable = true
            };

            skillDatabase.skills.Add(shieldSkill);
            skillDatabase.skills.Add(multiAttackSkill);
            skillDatabase.skills.Add(multiTriggerBuff);

            Debug.Log("测试技能创建完成！");
        }

        [ContextMenu("测试所有增强功能")]
        public void TestAllEnhancedFeatures()
        {
            Debug.Log("=== 测试所有增强功能 ===");
            
            TestSelfTargetSkill();
            yield return new WaitForSeconds(1f);
            
            TestMultiRandomTargetSkill();
            yield return new WaitForSeconds(1f);
            
            TestMultiTriggerSkill();
            yield return new WaitForSeconds(1f);
            
            ShowEnhancedSkillInfo();
        }

        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 320, 400, 200));
            GUILayout.Label("增强技能系统测试", EditorStyles.boldLabel);
            
            GUILayout.Label($"按 {testSelfTargetKey} 测试自身目标技能");
            GUILayout.Label($"按 {testMultiRandomKey} 测试多随机目标技能");
            GUILayout.Label($"按 {testMultiTriggerKey} 测试多触发类型技能");
            GUILayout.Label($"按 {showSkillInfoKey} 显示增强技能信息");
            
            GUILayout.Space(10);
            
            if (skillDatabase != null)
            {
                int enhancedSkillCount = 0;
                foreach (var skill in skillDatabase.skills)
                {
                    if (skill.targetType == TargetType.Self || 
                        (skill.triggerTypes != null && skill.triggerTypes.Count > 1) ||
                        (skill.targetType == TargetType.Random && skill.randomTargetCount > 1))
                    {
                        enhancedSkillCount++;
                    }
                }
                GUILayout.Label($"增强功能技能数量: {enhancedSkillCount}");
            }
            
            GUILayout.EndArea();
        }
    }
}
