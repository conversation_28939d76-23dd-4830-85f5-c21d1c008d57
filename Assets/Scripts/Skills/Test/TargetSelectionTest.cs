using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using EOP.Skills;
using EOP.combat;
using UnityEditor;

namespace EOP.Skills.Test
{
    public class TargetSelectionTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private SkillManager skillManager;
        [SerializeField] private TargetSelector targetSelector;
        [SerializeField] private CombatManager combatManager;
        
        [Header("测试技能")]
        [SerializeField] private string frontAttackSkillId = "ATTACK_FRONT";
        [SerializeField] private string backAttackSkillId = "ATTACK_BACK";
        [SerializeField] private string randomAttackSkillId = "ATTACK_RANDOM";
        [SerializeField] private string allAttackSkillId = "ATTACK_ALL";
        
        [Header("测试按键")]
        [SerializeField] private KeyCode testFrontAttackKey = KeyCode.F1;
        [SerializeField] private KeyCode testBackAttackKey = KeyCode.F2;
        [SerializeField] private KeyCode testRandomAttackKey = KeyCode.F3;
        [SerializeField] private KeyCode testAllAttackKey = KeyCode.F4;
        [SerializeField] private KeyCode previewTargetsKey = KeyCode.F5;

        private void Start()
        {
            // 查找系统组件
            if (skillManager == null)
                skillManager = FindObjectOfType<SkillManager>();
                
            if (targetSelector == null)
                targetSelector = FindObjectOfType<TargetSelector>();
                
            if (combatManager == null)
                combatManager = FindObjectOfType<CombatManager>();
        }

        private void Update()
        {
            CheckTestInputs();
        }

        private void CheckTestInputs()
        {
            // 测试前排攻击
            if (UnityEngine.Input.GetKeyDown(testFrontAttackKey))
            {
                TestTargetAttack(TargetType.Front, frontAttackSkillId);
            }
            
            // 测试后排攻击
            if (UnityEngine.Input.GetKeyDown(testBackAttackKey))
            {
                TestTargetAttack(TargetType.Back, backAttackSkillId);
            }
            
            // 测试随机攻击
            if (UnityEngine.Input.GetKeyDown(testRandomAttackKey))
            {
                TestTargetAttack(TargetType.Random, randomAttackSkillId);
            }
            
            // 测试群体攻击
            if (UnityEngine.Input.GetKeyDown(testAllAttackKey))
            {
                TestTargetAttack(TargetType.All, allAttackSkillId);
            }
            
            // 预览目标信息
            if (UnityEngine.Input.GetKeyDown(previewTargetsKey))
            {
                PreviewAllTargets();
            }
        }

        private void TestTargetAttack(TargetType targetType, string skillId)
        {
            if (targetSelector == null)
            {
                Debug.LogWarning("TargetSelector 未找到");
                return;
            }

            int testDamage = 5;
            
            Debug.Log($"=== 测试 {targetType} 攻击 ===");
            Debug.Log($"目标描述: {targetSelector.GetTargetDescription(targetType)}");
            Debug.Log($"目标数量: {targetSelector.GetTargetCount(targetType)}");
            Debug.Log($"目标有效性: {targetSelector.IsTargetTypeValid(targetType)}");
            Debug.Log($"攻击预览: {targetSelector.PreviewAttack(targetType, testDamage)}");
            
            if (targetSelector.IsTargetTypeValid(targetType))
            {
                targetSelector.ExecuteAttackByTargetType(targetType, testDamage);
            }
            else
            {
                Debug.Log("目标无效，无法执行攻击");
            }
        }

        private void PreviewAllTargets()
        {
            if (targetSelector == null)
            {
                Debug.LogWarning("TargetSelector 未找到");
                return;
            }

            Debug.Log("=== 所有目标类型预览 ===");
            
            foreach (TargetType targetType in System.Enum.GetValues(typeof(TargetType)))
            {
                string description = targetSelector.GetTargetDescription(targetType);
                int count = targetSelector.GetTargetCount(targetType);
                bool isValid = targetSelector.IsTargetTypeValid(targetType);
                string preview = targetSelector.PreviewAttack(targetType, 3);
                
                Debug.Log($"{targetType}: {description} | 数量: {count} | 有效: {isValid} | 预览: {preview}");
            }
        }

        [ContextMenu("测试所有目标类型")]
        public void TestAllTargetTypes()
        {
            if (targetSelector == null)
            {
                Debug.LogWarning("TargetSelector 未找到");
                return;
            }

            Debug.Log("=== 测试所有目标类型 ===");
            
            foreach (TargetType targetType in System.Enum.GetValues(typeof(TargetType)))
            {
                Debug.Log($"\n--- 测试 {targetType} ---");
                TestTargetAttack(targetType, "TEST_SKILL");
                
                // 等待一帧
                StartCoroutine(WaitOneFrame());
            }
        }

        [ContextMenu("测试技能系统集成")]
        public void TestSkillSystemIntegration()
        {
            if (skillManager == null)
            {
                Debug.LogWarning("SkillManager 未找到");
                return;
            }

            Debug.Log("=== 测试技能系统集成 ===");
            
            // 测试不同目标类型的技能
            skillManager.ProcessClearSkill(frontAttackSkillId, 2);
            skillManager.ProcessClearSkill(backAttackSkillId, 2);
            skillManager.ProcessClearSkill(randomAttackSkillId, 2);
            skillManager.ProcessClearSkill(allAttackSkillId, 2);
        }

        [ContextMenu("显示敌人信息")]
        public void ShowEnemyInfo()
        {
            if (combatManager == null)
            {
                Debug.LogWarning("CombatManager 未找到");
                return;
            }

            Debug.Log("=== 敌人信息 ===");
            Debug.Log($"敌人总数: {combatManager._combatEnemies.Count}");
            Debug.Log($"当前目标: {(combatManager.target != null ? combatManager.target.name : "无")}");
            
            for (int i = 0; i < combatManager._combatEnemies.Count; i++)
            {
                var enemy = combatManager._combatEnemies[i];
                Debug.Log($"敌人 {i}: {enemy.name} (HP: {enemy.GetCurrentHP()})");
            }
        }

        private IEnumerator WaitOneFrame()
        {
            yield return null;
        }

        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 400, 300));
            GUILayout.Label("目标选择测试", EditorStyles.boldLabel);
            
            GUILayout.Label($"按 {testFrontAttackKey} 测试前排攻击");
            GUILayout.Label($"按 {testBackAttackKey} 测试后排攻击");
            GUILayout.Label($"按 {testRandomAttackKey} 测试随机攻击");
            GUILayout.Label($"按 {testAllAttackKey} 测试群体攻击");
            GUILayout.Label($"按 {previewTargetsKey} 预览所有目标");
            
            GUILayout.Space(10);
            
            if (combatManager != null)
            {
                GUILayout.Label($"敌人数量: {combatManager._combatEnemies.Count}");
                GUILayout.Label($"当前目标: {(combatManager.target != null ? combatManager.target.name : "无")}");
            }
            
            if (targetSelector != null)
            {
                GUILayout.Space(10);
                GUILayout.Label("目标状态:");
                
                foreach (TargetType targetType in System.Enum.GetValues(typeof(TargetType)))
                {
                    bool isValid = targetSelector.IsTargetTypeValid(targetType);
                    int count = targetSelector.GetTargetCount(targetType);
                    GUILayout.Label($"{targetType}: {(isValid ? "✓" : "✗")} ({count})");
                }
            }
            
            GUILayout.EndArea();
        }
    }
}
