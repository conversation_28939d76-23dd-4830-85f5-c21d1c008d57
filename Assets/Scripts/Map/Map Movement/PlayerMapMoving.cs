using System;
using System.Collections;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

namespace EOP.Tetris
{
    public class PlayerMapMoving : MonoBehaviour
    {
        public TetrisMapGrid mapGrid;
        [ShowInInspector, ReadOnly] private Vector2Int _mapGridPos;

        private void Update()
        {
            UpdateGridPos(mapGrid.GetGridPos(transform.position));
            Movement();
        }

        private void Movement()
        {
            if (UnityEngine.Input.GetKeyDown(KeyCode.A))
            {
                transform.Translate(Vector3.left);
                if (!IsPosLegal())
                {
                    transform.Translate(Vector3.right);
                }
            }
            else if (UnityEngine.Input.GetKeyDown(KeyCode.D))
            {
                transform.Translate(Vector3.right);
                if (!IsPosLegal())
                {
                    transform.Translate(Vector3.left);
                }
            }
            else if (UnityEngine.Input.GetKeyDown(KeyCode.W))
            {
                transform.Translate(Vector3.up);
                if (!IsPosLegal())
                {
                    transform.Translate(Vector3.down);
                }
            }
            else if (UnityEngine.Input.GetKeyDown(KeyCode.S))
            {
                transform.Translate(Vector3.down);
                if (!IsPosLegal())
                {
                    transform.Translate(Vector3.up);
                }
            }
        }

        private bool IsPosLegal()
        {
            if (mapGrid.IsWithinGrid(mapGrid.GetGridPos(transform.position)))
            {
                MapNode node = mapGrid.HasMapNode(mapGrid.GetGridPos(transform.position));
                if (node)
                {
                    node.EvokeNodeEvent();
                    return true;
                }
                return mapGrid.HasPath(mapGrid.GetGridPos(transform.position));
            }

            return false;

            

        }

        public void UpdateGridPos(Vector2Int gridPos)
        {
            _mapGridPos = gridPos;
            transform.position = mapGrid.GetWorldPos(gridPos);
        }
    }
}