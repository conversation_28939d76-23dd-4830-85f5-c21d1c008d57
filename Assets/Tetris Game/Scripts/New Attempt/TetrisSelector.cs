using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using EOP.combat;
namespace EOP.Tetris
{
    public class TetrisSelector : MonoBehaviour
    {
        public GameObject[] tetrominos; 
        public TetrisGrid grid;
        public Vector2Int spawnGridPos; 

        public GameObject[] previewTetrominos = new GameObject[3]; // store 3 preview tetro
        //private Vector3[] previewPositions = { new Vector3(-8, 10, 0), new Vector3(-8, 5, 0), new Vector3(-8, 0, 0) }; // test positon

        public Button[] selectButtons; 

        public GameObject storedTetromino = null; // 1 tetro in storage
        public Button storageButton;
        public CombatManager _combatManager;
        
        private TetrominoEntity _activeTetromino;
        public Transform selectionMinosParent;
        public GameObject toolTip;
        private TetrominoBankGeneration tetrominoBankGenerator;
        private int tetrominoID;
        
        private void Start()
        {
            tetrominoBankGenerator = FindObjectOfType<TetrominoBankGeneration>();
            tetrominos = tetrominoBankGenerator.playerTetrominoBank;
            GeneratePreviewTetrominos();
            AssignButtonListeners();
            _combatManager = FindObjectOfType<CombatManager>();
            _combatManager.UpdateUIs();
        }

        private void Update()
        {
            if (IsPreviewEmpty())
            {
                Debug.Log("Preview area is empty. Refreshing preview tetrominos...");
                GeneratePreviewTetrominos();
            }
        }

        private void GeneratePreviewTetrominos()
        {
            // random select 3 tetro from the tetrominos array, dont repeat
            List<int> indexes = new List<int>();
            for (int i = 0; i < 3; i++)
            {
                int index = Random.Range(0, tetrominos.Length);
                while (indexes.Contains(index))
                {
                    index = Random.Range(0, tetrominos.Length);
                }
                indexes.Add(index);
            }
            for (int i = 0; i < previewTetrominos.Length; i++)
            {
                if (previewTetrominos[i] != null)
                {
                    Destroy(previewTetrominos[i]);
                }
                // instantiate random tetro
                previewTetrominos[i] = Instantiate(
                    tetrominos[indexes[i]], 
                    selectButtons[i].transform.position, 
                    Quaternion.identity 
                );
                previewTetrominos[i].transform.localScale *= grid.gridSize; 
                previewTetrominos[i].transform.SetParent(selectionMinosParent);
                previewTetrominos[i].name = "Preview Tetromino " + i;
                previewTetrominos[i].GetComponent<TetrominoEntity>().enabled = false; // 禁用逻辑，使其仅用于显示
            }
            ColorPreviewTetrominos();
            if (_combatManager != null)
            {
                _combatManager.UpdateUIs();
            }
        }

        public void ColorPreviewTetrominos()
        {
            for (int i = 0; i < previewTetrominos.Length; i++)
            {
                if (previewTetrominos[i] != null)
                {
                    string color = previewTetrominos[i].GetComponent<TetrominoEntity>().colorName;
                    previewTetrominos[i].GetComponent<TetrominoEntity>().SetMinosColor(color);
                }
            }
        }

        private void AssignButtonListeners()
        {
            for (int i = 0; i < selectButtons.Length; i++)
            {
                int index = i; // 避免闭包问题
                EventTrigger trigger = selectButtons[i].gameObject.AddComponent<EventTrigger>();

                // 左键点击逻辑
                EventTrigger.Entry leftClickEntry = new EventTrigger.Entry
                {
                    eventID = EventTriggerType.PointerClick
                };
                leftClickEntry.callback.AddListener((data) =>
                {
                    PointerEventData pointerData = (PointerEventData)data;
                    if (pointerData.button == PointerEventData.InputButton.Left)
                    {
                        SpawnTetromino(index);
                    }
                });
                trigger.triggers.Add(leftClickEntry);

                // 右键点击逻辑
                EventTrigger.Entry rightClickEntry = new EventTrigger.Entry
                {
                    eventID = EventTriggerType.PointerClick
                };
                rightClickEntry.callback.AddListener((data) =>
                {
                    PointerEventData pointerData = (PointerEventData)data;
                    if (pointerData.button == PointerEventData.InputButton.Right)
                    {
                        StoreTetromino(index);
                    }
                });
                trigger.triggers.Add(rightClickEntry);

                // 鼠标进入逻辑
                EventTrigger.Entry enterEntry = new EventTrigger.Entry
                {
                    eventID = EventTriggerType.PointerEnter
                };
                enterEntry.callback.AddListener((data) =>
                {
                    Debug.Log("Mouse enter button " + index);
                    toolTip.SetActive(true);
                    toolTip.transform.position = selectButtons[index].transform.position + new Vector3(0, 1.5f, 0);
                    toolTip.GetComponentInChildren<Text>().text = previewTetrominos[index].GetComponent<TetrominoEntity>().tetrominoName;
                });

                // 鼠标移出逻辑
                EventTrigger.Entry exitEntry = new EventTrigger.Entry
                {
                    eventID = EventTriggerType.PointerExit
                };
                exitEntry.callback.AddListener((data) =>
                {
                    toolTip.SetActive(false);
                });
            }
            // 为存储按钮添加左键监听
            EventTrigger storageTrigger = storageButton.gameObject.AddComponent<EventTrigger>();
            EventTrigger.Entry storageClickEntry = new EventTrigger.Entry
            {
                eventID = EventTriggerType.PointerClick
            };
            storageClickEntry.callback.AddListener((data) =>
            {
                PointerEventData pointerData = (PointerEventData)data;
                if (pointerData.button == PointerEventData.InputButton.Left)
                {
                    SpawnStoredTetromino();
                }
            });
            storageTrigger.triggers.Add(storageClickEntry);

        }


        private bool CanSpawnTetromino()
        {
            if (_activeTetromino == null)
            {
                return true;
            }
            return !_activeTetromino.enabled;
        }
        private void SpawnTetromino(int index)
        {
            if (index < 0 || index >= previewTetrominos.Length || previewTetrominos[index] == null)
            {
                Debug.LogError("Invalid tetromino selection.");
                return;
            }

            if (!CanSpawnTetromino())
            {
                return;
            }

            // put it on the top of the board
            GameObject spawnedTetromino = Instantiate(
                previewTetrominos[index],
                grid.GetWorldPos(spawnGridPos),
                Quaternion.identity
            );
            
            _activeTetromino = spawnedTetromino.GetComponent<TetrominoEntity>();
            if (_activeTetromino != null)
            {
                spawnedTetromino.name = "Tetromino " + tetrominoID;
                tetrominoID++;
                spawnedTetromino.transform.parent = grid.transform;
                _activeTetromino.enabled = true;
                _activeTetromino.Initialize(grid, this);
            }
            else
            {
                Debug.LogError("Spawned tetromino does not have a TetrominoEntity component.");
            }

            // clean it
            Destroy(previewTetrominos[index]);
            previewTetrominos[index] = null;

            // roll
            GeneratePreviewTetrominos();
        }
        private void StoreTetromino(int index)
        {
            if (index < 0 || index >= previewTetrominos.Length || previewTetrominos[index] == null)
            {
                Debug.LogError("Invalid tetromino selection for storage.");
                return;
            }

            
            if (storedTetromino != null)
            {
                Destroy(storedTetromino);
            }

            
            storedTetromino = Instantiate(previewTetrominos[index], storageButton.transform.position, Quaternion.identity);
            storedTetromino.transform.localScale *= grid.gridSize;

             if (storedTetromino != null)
            {
                    string color = storedTetromino.GetComponent<TetrominoEntity>().colorName;
                    storedTetromino.GetComponent<TetrominoEntity>().SetMinosColor(color);
            }


            var tetrominoEntity = storedTetromino.GetComponent<TetrominoEntity>();
            if (tetrominoEntity != null)
            {
                tetrominoEntity.enabled = false; // 禁用逻辑，仅用于显示
            }

            
            Destroy(previewTetrominos[index]);
            previewTetrominos[index] = null;

            //GeneratePreviewTetrominos();
        }

        private void SpawnStoredTetromino()
        {
            if (storedTetromino == null)
            {
                Debug.LogError("No tetromino stored.");
                return;
            }
            
            if (!CanSpawnTetromino())
            {
                return;
            }

            GameObject spawnedTetromino = Instantiate(storedTetromino, grid.GetWorldPos(spawnGridPos), Quaternion.identity);

            _activeTetromino = spawnedTetromino.GetComponent<TetrominoEntity>();
            if (_activeTetromino != null)
            {
                _activeTetromino.enabled = true;
                _activeTetromino.Initialize(grid, this);
            }
            else
            {
                Debug.LogError("Stored tetromino does not have a TetrominoEntity component.");
            }

            Destroy(storedTetromino);
            storedTetromino = null;
        }
        private bool IsPreviewEmpty()
        {

            foreach (var tetromino in previewTetrominos)
            {
                if (tetromino != null)
                {
                    return false;
                }
            }
            return true;
        }

    }
}
